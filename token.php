<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>Token管理 - iDatas</title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" href="/assets/css/codebase.min-5.4.css">
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        :root {
            --primary-color: #007AFF;
            --success-color: #34C759;
            --warning-color: #FF9500;
            --danger-color: #FF3B30;
            --background-color: #F2F2F7;
            --card-background: #FFFFFF;
            --text-primary: #000000;
            --text-secondary: #8E8E93;
        }

        body {
            background-color: var(--background-color);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }

        .main-section {
            background: var(--card-background);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 20px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 10px;
            color: var(--primary-color);
        }

        .form-control {
            border-radius: 12px;
            padding: 12px 15px;
            border: 2px solid #E5E5EA;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0,122,255,0.1);
        }

        .btn-ios {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 15px;
        }

        .btn-ios:hover {
            background: #0062CC;
            transform: scale(1.02);
        }

        .secondary-section {
            background: var(--card-background);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .price-card {
            background: var(--card-background);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            transition: transform 0.3s ease;
        }

        .price-card:hover {
            transform: translateY(-5px);
        }

        .price-card.popular {
            border: 2px solid var(--primary-color);
            position: relative;
        }

        .price-card.popular::before {
            content: "最受欢迎";
            position: absolute;
            top: -12px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
        }

        .price-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--text-primary);
        }

        .price-amount {
            font-size: 36px;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 15px;
        }

        .price-features {
            list-style: none;
            padding: 0;
            margin: 0 0 20px 0;
        }

        .price-features li {
            padding: 8px 0;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
        }

        .price-features li i {
            margin-right: 8px;
            color: var(--success-color);
        }

        .alert {
            border-radius: 12px;
            padding: 15px;
            margin-top: 15px;
        }

        .alert i {
            margin-right: 8px;
        }

        .floating-badge {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--warning-color);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            animation: bounce 2s infinite;
            z-index: 1000;
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
<div id="page-container" class="enable-page-overlay side-scroll page-header-fixed main-content-boxed">
    <main id="main-container">
        <div class="content">
            <!-- 主要功能区 -->
            <div class="row">
                <!-- Token注册 -->
                <div class="col-md-6">
                    <div class="main-section">
                        <h2 class="section-title">
                            <i class="fas fa-user-plus"></i>Token注册
                        </h2>
                        <form id="registerForm">
                            <div class="form-group">
                                <div class="input-group mb-3">
                                    <input type="text" class="form-control" id="registerToken" name="token" placeholder="请输入Token">
                                    <div class="input-group-append">
                                        <span class="input-group-text" style="font-size: 12px; color: var(--text-secondary);">字母大小写与数字组合，不超过18位</span>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="cardKey" name="card_key" placeholder="请输入卡密">
                                        <div class="input-group-append">
                                            <a href="#pricing" class="input-group-text text-danger" style="font-size: 12px; text-decoration: none; cursor: pointer;" onclick="scrollToPricing(event)">购买</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-ios" id="registerBtn">注册</button>
                        </form>
                        <div id="registerInfo"></div>
                    </div>
                </div>

                <!-- Token查询 -->
                <div class="col-md-6">
                    <div class="main-section">
                        <h2 class="section-title">
                            <i class="fas fa-search"></i>Token查询
                        </h2>
                        <div class="form-group">
                            <input type="text" class="form-control" id="queryToken" placeholder="请输入Token">
                        </div>
                        <button type="button" class="btn btn-ios" id="queryBtn">查询</button>
                        <div id="tokenInfo"></div>
                    </div>
                </div>
            </div>

            <!-- 次要功能区 -->
            <div class="row mt-4" id="pricing">
                <div class="col-12">
                    <h3 class="section-title">
                        <i class="fas fa-tags"></i>卡密套餐
                    </h3>
                </div>
                <div class="col-md-3">
                    <div class="price-card">
                        <h3 class="price-title">月卡</h3>
                        <div class="price-amount">¥69</div>
                        <ul class="price-features">
                            <li><i class="fas fa-check"></i> 30天使用时长</li>
                            <li><i class="fas fa-check"></i> 基础API支持</li>
                            <li><i class="fas fa-check"></i> 标准响应速度</li>
                        </ul>
                        <a href="https://sc.ymy9.com/shop/idatas/6qlaz9" class="btn btn-ios">立即购买</a>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="price-card">
                        <h3 class="price-title">季卡</h3>
                        <div class="price-amount">¥113</div>
                        <ul class="price-features">
                            <li><i class="fas fa-check"></i> 90天使用时长</li>
                            <li><i class="fas fa-check"></i> 高级API支持</li>
                            <li><i class="fas fa-check"></i> 优先响应速度</li>
                        </ul>
                        <a href="https://sc.ymy9.com/shop/idatas/6qlaz9" class="btn btn-ios">立即购买</a>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="price-card popular">
                        <h3 class="price-title">年卡</h3>
                        <div class="price-amount">¥240</div>
                        <ul class="price-features">
                            <li><i class="fas fa-check"></i> 365天使用时长</li>
                            <li><i class="fas fa-check"></i> 全部API支持</li>
                            <li><i class="fas fa-check"></i> 极速响应</li>
                        </ul>
                        <a href="https://sc.ymy9.com/shop/idatas/6qlaz9" class="btn btn-ios">立即购买</a>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="price-card">
                        <h3 class="price-title">永久卡</h3>
                        <div class="price-amount">¥361</div>
                        <ul class="price-features">
                            <li><i class="fas fa-check"></i> 永久使用</li>
                            <li><i class="fas fa-check"></i> 全部API支持</li>
                            <li><i class="fas fa-check"></i> 终身技术支持</li>
                        </ul>
                        <a href="https://sc.ymy9.com/shop/idatas/6qlaz9" class="btn btn-ios">立即购买</a>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- 限时优惠提示 -->
<div class="floating-badge">
    <i class="fas fa-gift"></i> 限时优惠：年卡立减20元！
</div>

<script src="/assets/js/codebase.app.min-5.4.js"></script>
<script src="https://cdn.jsdelivr.net/npm/wow.js@1.2.2/dist/wow.min.js"></script>
<script>
$(document).ready(function() {
    // 初始化动画
    new WOW().init();

    // 滚动到价格区域
    window.scrollToPricing = function(event) {
        event.preventDefault();
        $('html, body').animate({
            scrollTop: $("#pricing").offset().top - 20
        }, 500);
    };

    // Token查询
    $('#queryBtn').click(function() {
        const token = $('#queryToken').val().trim();
        if (!token) {
            alert('请输入Token');
            return;
        }
        
        $.get(`https://api.qnm6.top/api/demo.php?token=${token}`, function(response) {
            if (response.code === 200) {
                const user = response.user;
                let html = `
                    <div class="alert alert-info animate__animated animate__fadeIn">
                        <p><i class="fas fa-key"></i> Token: ${user.token}</p>
                        <p><i class="fas fa-clock"></i> 注册时间: ${user.time}</p>
                        <p><i class="fas fa-crown"></i> VIP状态: ${user.vipcode === '1' ? '是' : '否'}</p>
                        <p><i class="fas fa-calendar-alt"></i> VIP到期时间: ${user.viptime || '未开通'}</p>
                        <p><i class="fas fa-info-circle"></i> Token状态: ${user.tokencode}</p>
                    </div>
                `;
                $('#tokenInfo').html(html);
            } else {
                $('#tokenInfo').html(`<div class="alert alert-danger animate__animated animate__shakeX">${response.message}</div>`);
            }
        });
    });

    // Token注册
    $('#registerBtn').click(function() {
        const token = $('#registerToken').val().trim();
        const cardKey = $('#cardKey').val().trim();
        
        if (!token || !cardKey) {
            alert('请填写完整信息');
            return;
        }
        
        // 显示加载状态
        $('#registerInfo').html('<div class="alert alert-info animate__animated animate__fadeIn">正在处理，请稍候...</div>');
        
        // 验证卡密并注册Token
        $.ajax({
            url: 'verify_card.php',
            type: 'POST',
            data: {
                token: token,
                card_key: cardKey
            },
            dataType: 'json',
            success: function(response) {
                if (response.code === 200) {
                    $('#registerInfo').html(`
                        <div class="alert alert-success animate__animated animate__fadeIn">
                            <p><i class="fas fa-check-circle"></i> 注册成功！</p>
                            <p><i class="fas fa-key"></i> Token: ${token}</p>
                            <p><i class="fas fa-calendar-alt"></i> VIP到期时间: ${response.user.viptime}</p>
                        </div>
                    `);
                    // 清空表单
                    $('#registerForm')[0].reset();
                } else {
                    $('#registerInfo').html(`<div class="alert alert-danger animate__animated animate__shakeX">${response.message}</div>`);
                }
            },
            error: function() {
                $('#registerInfo').html('<div class="alert alert-danger animate__animated animate__shakeX">网络错误，请稍后重试</div>');
            }
        });
    });
});
</script>
</body>
</html> 