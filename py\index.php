<?php
// 直接在页面内定义数据库连接参数
$param = array(
  'host' => 'localhost',
  'port' => '3306',
  'user' => 'api269sjk135',
  'pwd' => 'api269sjk135',
  'dbname' => 'api269sjk135',
  'charset' => 'utf8',
);

try {
    // 创建数据库连接
    $pdo = new PDO("mysql:host={$param['host']};dbname={$param['dbname']};charset={$param['charset']}", $param['user'], $param['pwd']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION); // 设置错误模式
} catch (PDOException $e) {
    die("数据库连接失败: " . $e->getMessage());
}

// 查询“分享”表中的所有数据，按“下载次数”从大到小排序
$sql = "SELECT * FROM 分享 ORDER BY `下载次数` DESC";
$stmt = $pdo->query($sql);
$shares = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资源分享</title>
    <link rel="stylesheet" href="static/css/style.css">
</head>
<body>
   

    <div class="topbar">
        <h1>资源共享</h1>
        <p>合作共赢</p>
        <p>这个页面会分享一系列资源, 您的产品, 我们来宣传, 您的广告,我们来传播 投稿：@nanfengr</p>

        <p>排名不代表效果, 平台只负责资源传播, 真伪自行辨别！下载次数越多排名越靠前. </p>
    </div>

    <div class="content">
        <div class="list-container">
            <?php foreach ($shares as $share): ?>
                <!-- 每一个分享条目 -->
                <a href="../py/demo.php?id=<?= $share['文件id'] ?>" class="list-item-link">
                    <div class="list-item">
                        <!-- 左侧图标 -->
                        <div class="list-item-icon">
                            <img src="<?= ($share['分享类型'] == 'py脚本' || $share['分享类型'] == 'app') ? './img/py.png' : './img/default.png' ?>" alt="图标">
                        </div>
                        
                        <!-- 右侧内容 -->
                        <div class="list-item-content">
                            <div class="list-item-title"><?= htmlspecialchars($share['文件名称']) ?></div>
                            <div class="list-item-description"><?= mb_substr(htmlspecialchars($share['文件介绍']), 0, 20) ?>...</div>
                            <div class="list-item-footer">
                                <span class="file-size">文件大小：<?= htmlspecialchars($share['文件大小']) ?>    </span>
                               </div>
                               <div class="list-item-footer">
                               <span class="download-count">下载次数：<?= htmlspecialchars($share['下载次数']) ?>次</span>
                              </div>
                        </div>
                    </div>
                </a>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="bottom-bar">
        <div class="bottom-bar-item">
            <img src="../img/zy.png" alt="主页">
            <a href="../">主页</a>
        </div>
        <div class="bottom-bar-item">
            <img src="../img/user.png" alt="资源共享">
            <a href="../py">资源共享</a>
        </div>
    </div>
</body>
</html>
