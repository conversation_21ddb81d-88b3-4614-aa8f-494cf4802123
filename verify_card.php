<?php
header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['code' => 405, 'message' => '方法不允许']);
    exit;
}

// 获取并清理输入数据
$card_key = isset($_POST['card_key']) ? trim($_POST['card_key']) : '';
$token = isset($_POST['token']) ? trim($_POST['token']) : '';

// 调试信息
error_log("Received data - card_key: " . $card_key . ", token: " . $token);

if (empty($card_key) || empty($token)) {
    echo json_encode([
        'code' => 400, 
        'message' => '请输入卡密和Token',
        'debug' => [
            'card_key' => $card_key,
            'token' => $token
        ]
    ]);
    exit;
}

// 读取卡密数据
$cards_file = 'cards.json';
if (!file_exists($cards_file)) {
    echo json_encode(['code' => 500, 'message' => '卡密数据文件不存在']);
    exit;
}

$cards = json_decode(file_get_contents($cards_file), true);
if ($cards === null) {
    echo json_encode(['code' => 500, 'message' => '卡密数据文件损坏']);
    exit;
}

// 查找卡密
$found = false;
$card = null;
$card_index = -1;
foreach ($cards as $index => $c) {
    if ($c['card_key'] === $card_key && $c['status'] === 0) {
        $found = true;
        $card = $c;
        $card_index = $index;
        break;
    }
}

if (!$found) {
    echo json_encode(['code' => 404, 'message' => '卡密不存在或已被使用']);
    exit;
}

// 尝试注册Token
$register_url = "https://api.qnm6.top/api/zhuce.php?token=" . urlencode($token);
error_log("Register URL: " . $register_url);

$register_response = @file_get_contents($register_url);
if ($register_response === false) {
    echo json_encode(['code' => 500, 'message' => '注册API请求失败']);
    exit;
}

error_log("Register Response: " . $register_response);

$register_result = json_decode($register_response, true);
if ($register_result === null) {
    echo json_encode([
        'code' => 500, 
        'message' => '注册API响应解析失败',
        'debug' => [
            'response' => $register_response,
            'json_error' => json_last_error_msg()
        ]
    ]);
    exit;
}

error_log("Register Result: " . print_r($register_result, true));

if (!isset($register_result['code']) || $register_result['code'] !== 200) {
    $error_message = isset($register_result['message']) ? $register_result['message'] : '未知错误';
    echo json_encode([
        'code' => 400, 
        'message' => 'Token注册失败：' . $error_message,
        'debug' => [
            'response' => $register_result
        ]
    ]);
    exit;
}

// 注册成功，进行充值
$cz_url = "https://api.qnm6.top/api/cz.php?token=" . urlencode($token) . "&time=" . $card['days'];
error_log("CZ URL: " . $cz_url);

$cz_response = @file_get_contents($cz_url);
if ($cz_response === false) {
    echo json_encode(['code' => 500, 'message' => '充值API请求失败']);
    exit;
}

error_log("CZ Response: " . $cz_response);

$cz_result = json_decode($cz_response, true);
if ($cz_result === null) {
    echo json_encode([
        'code' => 500, 
        'message' => '充值API响应解析失败',
        'debug' => [
            'response' => $cz_response,
            'json_error' => json_last_error_msg()
        ]
    ]);
    exit;
}

error_log("CZ Result: " . print_r($cz_result, true));

if (!isset($cz_result['code']) || $cz_result['code'] !== 200) {
    $error_message = isset($cz_result['message']) ? $cz_result['message'] : '未知错误';
    echo json_encode([
        'code' => 400, 
        'message' => '充值失败：' . $error_message,
        'debug' => [
            'response' => $cz_result
        ]
    ]);
    exit;
}

// 注册和充值都成功，标记卡密为已使用
$cards[$card_index]['status'] = 1;
$cards[$card_index]['used_time'] = date('Y-m-d H:i:s');
$cards[$card_index]['used_token'] = $token;

// 保存更新后的卡密数据
file_put_contents($cards_file, json_encode($cards, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

echo json_encode([
    'code' => 200,
    'message' => '卡密使用成功',
    'days' => $card['days'],
    'user' => $cz_result['user'] ?? null
]);
?> 