<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 腾讯地图API KEY（可替换为你的KEY）
$tencent_map_key = 'LFJBZ-XJT3G-HFVQZ-IGOMK-YZ7RO-BKFNL';

$phone = isset($_GET['phone']) ? trim($_GET['phone']) : '';
$token = isset($_GET['token']) ? trim($_GET['token']) : '';

if (empty($phone) || empty($token)) {
    echo json_encode([
        'code' => 400,
        'message' => '请传递手机号参数phone和token'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

include './verify_vip.php';

// 会员权限校验
$vipTimeLimit = false;  // 是否启用会员时间限制
$vipCodeLimit = true;  // 是否启用会员功能限制
$callback = function($vipcode, $viptime) use ($vipTimeLimit, $vipCodeLimit) {
    if ($vipCodeLimit) {
        $result = vipCodeLimit($vipcode);
        if ($result !== true) return $result;
    }
    if ($vipTimeLimit) {
        $result = vipTimeLimit($viptime);
        if ($result !== true) return $result;
    }
    return true;
};
$verificationResult = verifyVipStatus($token, $callback);
if ($verificationResult !== true) {
    echo json_encode($verificationResult, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

// 1. 获取手机号归属地
$api_url = 'https://api.pearktrue.cn/api/gsd/?tel=' . urlencode($phone);
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);
$api_response = curl_exec($ch);
$curl_error = curl_error($ch);
curl_close($ch);

if ($api_response === false) {
    echo json_encode([
        'code' => 500,
        'message' => '手机号归属地API请求失败: ' . $curl_error
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$data = json_decode($api_response, true);
if (!isset($data['code']) || $data['code'] != 200) {
    echo json_encode([
        'code' => 500,
        'message' => '手机号归属地API错误: ' . ($data['msg'] ?? '未知错误')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$province = $data['data']['province'] ?? '';
$city = $data['data']['city'] ?? '';
$operator = $data['data']['server'] ?? '';

// 2. 获取经纬度
$geo_url = 'https://apis.map.qq.com/ws/geocoder/v1/?address=' . urlencode($city) . '&key=' . $tencent_map_key;
$geo_json = file_get_contents($geo_url);
$geo_data = json_decode($geo_json, true);
if (!isset($geo_data['status']) || $geo_data['status'] != 0) {
    echo json_encode([
        'code' => 500,
        'message' => '腾讯地图API获取经纬度失败: ' . ($geo_data['message'] ?? '未知错误')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}
$lat = $geo_data['result']['location']['lat'];
$lng = $geo_data['result']['location']['lng'];

// 3. 获取详细地址
$detail_url = 'https://apis.map.qq.com/ws/geocoder/v1/?location=' . $lat . ',' . $lng . '&key=' . $tencent_map_key . '&coord_type=5&get_poi=0';
$detail_json = file_get_contents($detail_url);
$detail_data = json_decode($detail_json, true);
if (!isset($detail_data['status']) || $detail_data['status'] != 0) {
    $detailed_address = '';
} else {
    $ac = $detail_data['result']['address_component'] ?? [];
    $detailed_address = ($ac['province'] ?? '') . ($ac['city'] ?? '') . ($ac['district'] ?? '') . ($ac['street'] ?? '') . ($ac['street_number'] ?? '');
    if (trim($detailed_address) === '') {
        $detailed_address = $detail_data['result']['address'] ?? '';
    }
}

// 返回结果
echo json_encode([
    'code' => 200,
    'message' => '查询成功',
    'data' => [
        'province' => $province,
        'city' => $city,
        'operator' => $operator,
        'lat' => $lat,
        'lng' => $lng,
        'address' => $detailed_address
    ]
], JSON_UNESCAPED_UNICODE);
exit; 