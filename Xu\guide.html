<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>信息查询系统 - 使用指南</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        :root {
            --glass-bg: rgba(255, 255, 255, 0.7);
            --glass-border: rgba(255, 255, 255, 0.3);
            --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: var(--glass-shadow);
            background: var(--glass-bg);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border: 1px solid var(--glass-border);
            margin-bottom: 20px;
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-header {
            background: linear-gradient(135deg, rgba(52, 58, 64, 0.9) 0%, rgba(73, 80, 87, 0.9) 100%);
            color: white;
            padding: 1.5rem;
            border-bottom: 1px solid var(--glass-border);
        }
        
        .notice {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid var(--glass-border);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }
        
        .notice h4 {
            color: #dc3545;
            margin-bottom: 15px;
        }
        
        .notice p {
            margin-bottom: 10px;
            color: #495057;
        }
        
        .notice a {
            color: #0d6efd;
            text-decoration: none;
        }
        
        .notice a:hover {
            text-decoration: underline;
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #0d6efd;
        }
        
        .nav-link {
            color: #495057;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 10px;
            transition: all 0.3s ease;
            display: inline-block;
            margin: 5px;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .nav-link:hover {
            background: #0d6efd;
            color: white;
            transform: translateY(-2px);
        }
        
        .step {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            border: 1px solid var(--glass-border);
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            background: #0d6efd;
            color: white;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }
        
        .data-source {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid var(--glass-border);
        }
        
        .data-source h5 {
            color: #0d6efd;
            margin-bottom: 10px;
        }
        
        .data-source p {
            margin-bottom: 5px;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="notice animate__animated animate__fadeIn">
            <h4><i class="fas fa-exclamation-triangle me-2"></i>重要公告</h4>
            <p>本程序由泪绪团队制作 ⚠️</p>
            <p>- 作者：泪绪@qingxu6nb</p>
            <p>- 官方唯一授权售卖渠道：泪绪 <a href="https://t.me/qingxu6nb666" target="_blank">https://t.me/qingxu6nb666</a></p>
            <p class="text-danger"><i class="fas fa-exclamation-circle me-2"></i>❗️ 严禁倒卖，泛滥，镜像，一旦发现立即封卡 ❗️</p>
        </div>

        <div class="card animate__animated animate__fadeInUp">
            <div class="card-header">
                <h2 class="mb-0"><i class="fas fa-info-circle me-2"></i>系统介绍</h2>
            </div>
            <div class="card-body">
                <p class="lead">欢迎使用信息查询系统，这是一个功能强大的信息查询平台，支持多种数据源的查询。</p>
                
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-search feature-icon"></i>
                            <h4>快速查询</h4>
                            <p>支持多数据源快速查询，响应迅速</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-shield-alt feature-icon"></i>
                            <h4>安全可靠</h4>
                            <p>Token验证机制，确保数据安全</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-database feature-icon"></i>
                            <h4>数据丰富</h4>
                            <p>支持多个数据源，信息全面</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card animate__animated animate__fadeInUp">
            <div class="card-header">
                <h2 class="mb-0"><i class="fas fa-map-signs me-2"></i>快速导航</h2>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <a href="index.php" class="nav-link">
                        <i class="fas fa-home me-2"></i>首页查询
                    </a>
                    <a href="doc.html" class="nav-link">
                        <i class="fas fa-book me-2"></i>API文档
                    </a>
                </div>
            </div>
        </div>

        <div class="card animate__animated animate__fadeInUp">
            <div class="card-header">
                <h2 class="mb-0"><i class="fas fa-list-ol me-2"></i>使用步骤</h2>
            </div>
            <div class="card-body">
                <div class="step">
                    <span class="step-number">1</span>
                    <strong>获取Token</strong>
                    <p>联系管理员获取您的专属Token</p>
                </div>
                <div class="step">
                    <span class="step-number">2</span>
                    <strong>选择查询方式</strong>
                    <p>可以通过网页查询或API接口查询</p>
                </div>
                <div class="step">
                    <span class="step-number">3</span>
                    <strong>输入查询内容</strong>
                    <p>输入要查询的中文姓名</p>
                </div>
                <div class="step">
                    <span class="step-number">4</span>
                    <strong>获取结果</strong>
                    <p>系统将返回查询结果，包括身份证号等信息</p>
                </div>
            </div>
        </div>

        <div class="card animate__animated animate__fadeInUp">
            <div class="card-header">
                <h2 class="mb-0"><i class="fas fa-database me-2"></i>数据来源</h2>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="data-source">
                            <h5><i class="fas fa-id-card me-2"></i>基础数据</h5>
                            <p>48plc数据</p>
                            <p>随申码数据</p>
                            <p>学习通数据</p>
                            <p>全国学籍数据</p>
                            <p>社保数据</p>
                            <p>医保数据</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="data-source">
                            <h5><i class="fas fa-graduation-cap me-2"></i>教育数据</h5>
                            <p>浙江学籍数据</p>
                            <p>上海10E数据</p>
                            <p>江苏学籍数据</p>
                            <p>广东学籍数据</p>
                            <p>北京学籍数据</p>
                            <p>全国高考数据</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="data-source">
                            <h5><i class="fas fa-credit-card me-2"></i>金融数据</h5>
                            <p>银联数据</p>
                            <p>aurora独家数据</p>
                            <p>支付宝数据</p>
                            <p>微信支付数据</p>
                            <p>银行流水数据</p>
                            <p>信用卡数据</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="data-source">
                            <h5><i class="fas fa-phone me-2"></i>通讯数据</h5>
                            <p>chphone数据</p>
                            <p>移动数据</p>
                            <p>联通数据</p>
                            <p>电信数据</p>
                            <p>虚拟运营商数据</p>
                            <p>5G用户数据</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="data-source">
                            <h5><i class="fas fa-car me-2"></i>交通数据</h5>
                            <p>驾驶证数据</p>
                            <p>行驶证数据</p>
                            <p>车辆违章数据</p>
                            <p>ETC数据</p>
                            <p>高铁数据</p>
                            <p>航空数据</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="data-source">
                            <h5><i class="fas fa-home me-2"></i>房产数据</h5>
                            <p>房产证数据</p>
                            <p>不动产数据</p>
                            <p>房屋租赁数据</p>
                            <p>公积金数据</p>
                            <p>房贷数据</p>
                            <p>装修数据</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="data-source">
                            <h5><i class="fas fa-briefcase me-2"></i>就业数据</h5>
                            <p>社保数据</p>
                            <p>公积金数据</p>
                            <p>工资数据</p>
                            <p>个税数据</p>
                            <p>企业数据</p>
                            <p>招聘数据</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="data-source">
                            <h5><i class="fas fa-shopping-cart me-2"></i>消费数据</h5>
                            <p>淘宝数据</p>
                            <p>京东数据</p>
                            <p>拼多多数据</p>
                            <p>美团数据</p>
                            <p>饿了么数据</p>
                            <p>外卖数据</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card animate__animated animate__fadeInUp">
            <div class="card-header">
                <h2 class="mb-0"><i class="fas fa-question-circle me-2"></i>常见问题</h2>
            </div>
            <div class="card-body">
                <div class="step">
                    <h5><i class="fas fa-question me-2"></i>如何获取Token？</h5>
                    <p>请联系管理员获取您的专属Token</p>
                </div>
                <div class="step">
                    <h5><i class="fas fa-question me-2"></i>查询次数用完了怎么办？</h5>
                    <p>请联系管理员进行充值</p>
                </div>
                <div class="step">
                    <h5><i class="fas fa-question me-2"></i>支持哪些查询方式？</h5>
                    <p>支持网页查询和API接口查询两种方式</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 