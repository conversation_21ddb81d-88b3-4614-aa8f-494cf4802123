<?php
// 允许跨域访问
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json; charset=utf-8');

// Telegram机器人配置
define('BOT_TOKEN', '**********************************************');
define('API_URL', 'https://api.telegram.org/bot' . BOT_TOKEN);
define('GROUP_USERNAME', 'riverfbl');

// 获取输入的数据
$update = json_decode(file_get_contents('php://input'), true);

// 如果没有收到数据，直接退出
if (!$update) {
    exit('No data received');
}

// 处理消息
if (isset($update['message'])) {
    $message = $update['message'];
    $chat_id = $message['chat']['id'];
    $user_id = $message['from']['id'];
    $text = $message['text'] ?? '';
    $username = $message['from']['username'] ?? '';

    // 检查用户是否在指定群组中
    $member = json_decode(file_get_contents(API_URL . '/getChatMember?chat_id=@' . GROUP_USERNAME . '&user_id=' . $user_id), true);
    
    if ($member['ok'] && in_array($member['result']['status'], ['creator', 'administrator', 'member'])) {
        // 处理/start命令
        if (strpos($text, '/start') === 0) {
            // 检查是否有邀请码
            $invite_code = '';
            if (strlen($text) > 7) {
                $invite_code = substr($text, 7);
            }
            
            // 调用注册API
            $register_url = 'https://mika.qnm6.top/register.php?user_id=' . $user_id;
            if ($invite_code) {
                $register_url .= '&invite_code=' . urlencode($invite_code);
            }
            $register_result = json_decode(file_get_contents($register_url), true);
            
            if ($register_result['code'] == 200 || $register_result['code'] == 400) {
                // 显示菜单
                showMenu($chat_id, $user_id);
            } else {
                sendMessage($chat_id, '注册失败：' . $register_result['message']);
            }
        }
        // 处理个户查询功能
        elseif (preg_match('/^([^\s]+)\s+([^\s]+)$/', $text, $matches)) {
            $name = $matches[1];
            $idcard = $matches[2];
            
            // 调用功能API
            $url = 'https://mika.qnm6.top/gongneng.php?name=' . urlencode($name) . '&idcard=' . urlencode($idcard) . '&key=' . $user_id;
            $result = json_decode(file_get_contents($url), true);
            
            if ($result['code'] == 200) {
                // 发送图片
                sendPhoto($chat_id, $result['data']['image_url']);
                // 发送消息
                $response = "<b>查询结果</b>\n\n";
                $response .= "<i>本次查询消耗：</i> <code>25积分</code>\n";
                $response .= "<i>当前剩余积分：</i> <code>{$result['data']['balance']}</code>\n";
                $response .= "\n<u>返回数据：</u>\n";
                $response .= "<code>{$result['data']['info']}</code>";
                sendMessage($chat_id, $response);
            } else {
                sendMessage($chat_id, "<b>查询失败</b>\n\n" . $result['message']);
            }
        }
    } else {
        sendMessage($chat_id, '⚠️ 请先加入 @' . GROUP_USERNAME . ' 群组才能使用机器人功能。');
    }
}

// 处理回调查询
if (isset($update['callback_query'])) {
    $callback_query = $update['callback_query'];
    $chat_id = $callback_query['message']['chat']['id'];
    $data = $callback_query['data'];
    $user_id = $callback_query['from']['id'];
    
    if ($data === 'query_menu') {
        sendMessage($chat_id, "✏️ 请发送：姓名 身份证号\n例如：张三 110101199001011234");
    } elseif ($data === 'checkin') {
        // 调用签到API
        $checkin_url = 'https://mika.qnm6.top/checkin.php?key=' . $user_id;
        $checkin_result = json_decode(file_get_contents($checkin_url), true);
        
        if ($checkin_result['code'] == 200) {
            sendMessage($chat_id, '✅ 签到成功！获得5积分\n💰 当前积分：' . $checkin_result['data']['balance']);
            // 更新菜单显示
            showMenu($chat_id, $user_id);
        } else {
            sendMessage($chat_id, '❌ 签到失败：' . $checkin_result['message']);
        }
    }
    
    // 回复回调查询
    answerCallbackQuery($callback_query['id']);
}

// 显示菜单
function showMenu($chat_id, $user_id) {
    // 通过API获取用户数据
    $user_data = json_decode(file_get_contents('https://mika.qnm6.top/get_user.php?key=' . $user_id), true);
    
    if ($user_data['code'] != 200) {
        sendMessage($chat_id, '获取用户信息失败：' . ($user_data['message'] ?? '未知错误'));
        return;
    }
    
    $user = $user_data['data'];
    
    $menu_text = "<b>🤖 欢迎使用River情报系统</b>\n\n";
    $menu_text .= "<code>👤 用户ID：{$user_id}</code>\n";
    $menu_text .= "<code>💰 当前积分：{$user['balance']}</code>\n";
    $menu_text .= "<code>🎯 邀请码：{$user['invite_code']}</code>";
    
    $keyboard = [
        'inline_keyboard' => [
            [
                ['text' => '🔍 个户查询 (消耗25积分)', 'callback_data' => 'query_menu'],
                ['text' => '📅 每日签到', 'callback_data' => 'checkin'],
                ['text' => '💬 官方客服', 'url' => 'https://t.me/iDataskefu_bot']
            ]
        ]
    ];
    
    sendMessage($chat_id, $menu_text, $keyboard);
}

// 发送消息
function sendMessage($chat_id, $text, $keyboard = null) {
    $data = [
        'chat_id' => $chat_id,
        'text' => $text,
        'parse_mode' => 'HTML'
    ];
    
    if ($keyboard) {
        $data['reply_markup'] = json_encode($keyboard);
    }
    
    file_get_contents(API_URL . '/sendMessage?' . http_build_query($data));
}

// 发送图片
function sendPhoto($chat_id, $photo_url) {
    $data = [
        'chat_id' => $chat_id,
        'photo' => $photo_url
    ];
    
    file_get_contents(API_URL . '/sendPhoto?' . http_build_query($data));
}

// 回复回调查询
function answerCallbackQuery($callback_query_id) {
    $data = [
        'callback_query_id' => $callback_query_id
    ];
    
    file_get_contents(API_URL . '/answerCallbackQuery?' . http_build_query($data));
}