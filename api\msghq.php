<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');
require '../db.php';

// 获取软件信息
$sql = "SELECT * FROM msg LIMIT 1";
$result = $mysqli->query($sql);

// 检查是否有结果
if ($result->num_rows > 0) {
    $appInfo = $result->fetch_assoc();
    echo json_encode([
        "code" => 200,
        "message" => "获取信息成功",
        "msg" => $appInfo
    ], JSON_UNESCAPED_UNICODE);
} else {
    echo json_encode([
        "code" => 404,
        "message" => "没有找到软件信息",
        "msg" => null
    ], JSON_UNESCAPED_UNICODE);
}

$mysqli->close();
?>
