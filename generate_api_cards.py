#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import random
import string
import os
from datetime import datetime

class CardGenerator:
    def __init__(self, cards_file='api_cards.json'):
        self.cards_file = cards_file
        
    def generate_card_key(self, length=8):
        """生成随机卡密"""
        characters = string.ascii_letters + string.digits
        return ''.join(random.choice(characters) for _ in range(length))
    
    def load_cards(self):
        """加载现有卡密数据"""
        if not os.path.exists(self.cards_file):
            return []
        
        try:
            with open(self.cards_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return []
    
    def save_cards(self, cards):
        """保存卡密数据"""
        with open(self.cards_file, 'w', encoding='utf-8') as f:
            json.dump(cards, f, ensure_ascii=False, indent=2)
    
    def card_exists(self, card_key, cards):
        """检查卡密是否已存在"""
        return any(card['card_key'] == card_key for card in cards)
    
    def generate_cards(self, count, uses_per_card):
        """生成指定数量的卡密"""
        cards = self.load_cards()
        new_cards = []
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        generated_count = 0
        attempts = 0
        max_attempts = count * 10  # 防止无限循环
        
        while generated_count < count and attempts < max_attempts:
            attempts += 1
            card_key = self.generate_card_key()
            
            # 确保卡密唯一
            if not self.card_exists(card_key, cards) and not self.card_exists(card_key, new_cards):
                new_card = {
                    'card_key': card_key,
                    'remaining_uses': uses_per_card,
                    'created_time': current_time
                }
                new_cards.append(new_card)
                generated_count += 1
        
        if generated_count < count:
            print(f"警告: 只生成了 {generated_count} 个卡密，可能存在重复问题")
        
        # 添加到现有卡密列表
        cards.extend(new_cards)
        
        # 保存到文件
        self.save_cards(cards)
        
        return new_cards
    
    def list_cards(self):
        """列出所有卡密"""
        cards = self.load_cards()
        if not cards:
            print("没有找到任何卡密")
            return
        
        print(f"{'卡密':<15} {'剩余次数':<10} {'创建时间':<20} {'最后使用':<20}")
        print("-" * 70)
        
        for card in cards:
            last_used = card.get('last_used', '未使用')
            print(f"{card['card_key']:<15} {card['remaining_uses']:<10} {card['created_time']:<20} {last_used:<20}")
    
    def delete_card(self, card_key):
        """删除指定卡密"""
        cards = self.load_cards()
        original_count = len(cards)
        
        cards = [card for card in cards if card['card_key'] != card_key]
        
        if len(cards) < original_count:
            self.save_cards(cards)
            print(f"卡密 {card_key} 已删除")
            return True
        else:
            print(f"未找到卡密 {card_key}")
            return False
    
    def update_card_uses(self, card_key, new_uses):
        """更新卡密使用次数"""
        cards = self.load_cards()
        
        for card in cards:
            if card['card_key'] == card_key:
                old_uses = card['remaining_uses']
                card['remaining_uses'] = new_uses
                self.save_cards(cards)
                print(f"卡密 {card_key} 的使用次数已从 {old_uses} 更新为 {new_uses}")
                return True
        
        print(f"未找到卡密 {card_key}")
        return False
    


def main():
    generator = CardGenerator()
    
    while True:
        print("\n=== 卡密管理系统 ===")
        print("1. 生成卡密")
        print("2. 查看所有卡密")
        print("3. 删除卡密")
        print("4. 修改卡密使用次数")
        print("5. 退出")
        
        choice = input("\n请选择操作 (1-5): ").strip()
        
        if choice == '1':
            try:
                count = int(input("请输入要生成的卡密数量: "))
                uses = int(input("请输入每个卡密的使用次数: "))
                
                if count <= 0 or uses <= 0:
                    print("数量和使用次数必须大于0")
                    continue
                
                print(f"\n正在生成 {count} 个卡密，每个可使用 {uses} 次...")
                new_cards = generator.generate_cards(count, uses)
                
                print(f"\n成功生成 {len(new_cards)} 个卡密:")
                for card in new_cards:
                    print(f"  {card['card_key']} (可使用 {card['remaining_uses']} 次)")

                # 输出用"-"分割的格式
                card_keys = [card['card_key'] for card in new_cards]
                print(f"\n用'-'分割格式:")
                print('-'.join(card_keys))
                
            except ValueError:
                print("请输入有效的数字")
        
        elif choice == '2':
            generator.list_cards()
        
        elif choice == '3':
            card_key = input("请输入要删除的卡密: ").strip()
            if card_key:
                generator.delete_card(card_key)
            else:
                print("卡密不能为空")
        
        elif choice == '4':
            card_key = input("请输入要修改的卡密: ").strip()
            if not card_key:
                print("卡密不能为空")
                continue
            
            try:
                new_uses = int(input("请输入新的使用次数: "))
                if new_uses < 0:
                    print("使用次数不能为负数")
                    continue
                generator.update_card_uses(card_key, new_uses)
            except ValueError:
                print("请输入有效的数字")
        
        elif choice == '5':
            print("再见!")
            break
        
        else:
            print("无效的选择，请重新输入")

if __name__ == "__main__":
    main()
