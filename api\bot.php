<?php
// Telegram Bot Token
$botToken = "**********************************************";

// 获取 Telegram 的 POST 数据
$content = file_get_contents("php://input");
$update = json_decode($content, true);

// 检查更新是否有效
if (!$update || !isset($update["message"])) {
    exit("No valid data received.");
}

// 提取聊天 ID 和用户消息
$chatId = $update["message"]["chat"]["id"];
$userMessage = $update["message"]["text"];

// 如果用户发送 "你好"，回复 "你好"
if (trim($userMessage) === "你好") {
    $reply = "你好";

    // 构造 Telegram API 的 URL
    $url = "https://api.telegram.org/bot$botToken/sendMessage";

    // 发送消息
    $response = file_get_contents($url . "?chat_id=" . $chatId . "&text=" . urlencode($reply));

    // 可选：检查响应是否成功
    if (!$response) {
        error_log("Failed to send message");
    }
}
?>
