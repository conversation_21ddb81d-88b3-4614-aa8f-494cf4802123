

























































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































@media screen and (min-width:767px){#mobile-footer{display:none}}
#mobile-footer{height:50px;background:#fafafa;position:fixed;bottom:0;left:0;border-top:0;width:100%;z-index:9999999;}
#mobile-menu{display:block;margin:0 auto;}
#mobile-menu:after{content:'';display:block;height:0;clear:both}
#mobile-menu>li{width:20%;float:left;border-top:1px solid #e6e6eb;height:100%}
#mobile-menu>li>a:hover{color:#000;}
#mobile-menu>li>.active{color:#000;}
#mobile-menu>li>a{text-align:center;color:#777;display:block;line-height:12px;height:100%}
#mobile-menu>li>a>span{font-size:20px;display:block;line-height:30px}

.autlv {
  font-size: 10px;
  color: #fff;
  display: inline-block;
  vertical-align: text-top;
  font-weight: normal;
  border-radius: 2px;
  line-height: 1.4;
  padding: 0 4px;
  margin-left: 5px;
  letter-spacing: 0px;
}

.aut-0 {
  background: #d1d5dc;
}

.aut-1 {
  background: #448EF6;
}

.aut-2 {
  background: #f6b044;
}

.aut-3 {
  background: #c444f6;
}

.aut-4 {
  background: #f69644;
}

.aut-5 {
  background-image: -webkit-linear-gradient(0deg, #3ca5f6 0%, #a86af9 100%);
}

.aut-6 {
  background: #f64444;
}
.aside-hunter-authors {
  background: var(--background);
  margin-bottom: 15px;
}
.aside-hunter-authors .vs {
  border-radius: 100%;
}
.aside-hunter-authors ul {
  padding: 14px;
  list-style: none;
  line-height: 2.5;
}
.aside-hunter-authors .item {
  border-bottom: 1px dashed #eee;
  margin-bottom: 0px;
  position: relative;
  padding-bottom: 4px;
}
.aside-hunter-authors .item .hunter-avatar {
  float: left;
  line-height: 55px;
}
.aside-hunter-authors .item .hunter-avatar .vatar {
  position: relative;
}
.aside-hunter-authors .item .hunter-avatar .vatar img {
  width: 45px;
  border-radius: 100%;
}
.aside-hunter-authors .item .hunter-avatar .vatar .va_v_honor {
  position: absolute;
  bottom: 3px;
  right: 0px;
  width: 18px;
  height: 18px;
}
.aside-hunter-authors .item .item-main {
  min-height: 60px;
  margin-left: 60px;
  color: #393939;
}
.aside-hunter-authors .item .item-main h4 {
  font-size: 12px;
  line-height: 1;
  margin-bottom: 7px;
  font-weight: normal;
  color: #999;
}
.aside-hunter-authors .item .item-main h4 i {
  font-style: normal;
  color: #aaa;
  margin: 0 5px;
}
.haibao-shade {
    background: grey;
    z-index: 20;
    opacity: 0.6;
    position: fixed;
    pointer-events: auto;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    transition: opacity .25s linear;
}
@-webkit-keyframes ball-spin-fade-loader {
  50% {
    opacity: 0.3;
    -webkit-transform: scale(0.4);
            transform: scale(0.4); }

  100% {
    opacity: 1;
    -webkit-transform: scale(1);
            transform: scale(1); } }

@keyframes ball-spin-fade-loader {
  50% {
    opacity: 0.3;
    -webkit-transform: scale(0.4);
            transform: scale(0.4); }

  100% {
    opacity: 1;
    -webkit-transform: scale(1);
            transform: scale(1); } }
.ball-spin-fade-loader {
  position: relative;
  left: calc(50% - 25px);
  top: calc(50% - 25px);
}
.ball-spin-fade-loader_title {
  position: relative;
  left: calc(50% - 50px);
  top: calc(55% - 15px);
}
.ball-spin-fade-loader > div:nth-child(1) {
  top: 25px;
  left: 0;
  -webkit-animation: ball-spin-fade-loader 1s 0s infinite linear;
          animation: ball-spin-fade-loader 1s 0s infinite linear;
}
.ball-spin-fade-loader > div:nth-child(2) {
  top: 17.04545px;
  left: 17.04545px;
  -webkit-animation: ball-spin-fade-loader 1s 0.12s infinite linear;
          animation: ball-spin-fade-loader 1s 0.12s infinite linear;
}
.ball-spin-fade-loader > div:nth-child(3) {
  top: 0;
  left: 25px;
  -webkit-animation: ball-spin-fade-loader 1s 0.24s infinite linear;
          animation: ball-spin-fade-loader 1s 0.24s infinite linear;
}
.ball-spin-fade-loader > div:nth-child(4) {
  top: -17.04545px;
  left: 17.04545px;
  -webkit-animation: ball-spin-fade-loader 1s 0.36s infinite linear;
          animation: ball-spin-fade-loader 1s 0.36s infinite linear;
}
.ball-spin-fade-loader > div:nth-child(5) {
  top: -25px;
  left: 0;
  -webkit-animation: ball-spin-fade-loader 1s 0.48s infinite linear;
          animation: ball-spin-fade-loader 1s 0.48s infinite linear;
}
.ball-spin-fade-loader > div:nth-child(6) {
  top: -17.04545px;
  left: -17.04545px;
  -webkit-animation: ball-spin-fade-loader 1s 0.6s infinite linear;
          animation: ball-spin-fade-loader 1s 0.6s infinite linear;
}
.ball-spin-fade-loader > div:nth-child(7) {
  top: 0;
  left: -25px;
  -webkit-animation: ball-spin-fade-loader 1s 0.72s infinite linear;
          animation: ball-spin-fade-loader 1s 0.72s infinite linear;
}
.ball-spin-fade-loader > div:nth-child(8) {
  top: 17.04545px;
  left: -17.04545px;
  -webkit-animation: ball-spin-fade-loader 1s 0.84s infinite linear;
          animation: ball-spin-fade-loader 1s 0.84s infinite linear;
}
.ball-spin-fade-loader > div {
  background-color: #fff;
  width: 15px;
  height: 15px;
  border-radius: 100%;
  margin: 2px;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
  position: absolute;
}
.haibao {
    width: 42px;
    height: 42px;
    background-color: #f56c6c;
    border-radius: 50%;
    cursor: pointer;
    position: relative;
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    color:#909399;
    font-size:12px
}
.haibao:hover {
    background-color: #409eff;
}
.article-poster {
    position:absolute;z-index:999;
}
/*弹出*/
.footer_flex { 
    width: 42px; 
    height: 42px; 
    background-color: #f56c6c; 
    border-radius: 50%; 
    cursor: pointer; 
    position: relative; 
    z-index: 10; 
    display: flex; 
    justify-content: center; 
    align-items: center; 
    color:#909399; 
    font-size:12px;
}
.footer_flex:hover { 
    background-color: #409eff;
}
/*top*/
.footer_flex:hover .flex-footer { 
    display: block; 
}
.footer_flex .flex-footer {
    box-shadow: 0px 0px 5px 0px #409eff;
    border-radius: 8px; 
    width: 156px;
    height: 166px; 
    position: absolute; 
    left: -52px; top: -175px; 
    text-align: center; 
    padding-top: 15px; 
    background: #fff; 
    display: none; 
}
.flex-footer input{
    vertical-align:middle; 
    margin-bottom:3px; 
    *margin-bottom:3px;
}

.joe_config__aside .tabs #config_custom{
    border-radius:20px;
    text-align:center;
    height:40px;
    line-height:40px;
    color:#606266;
    cursor:pointer;
    transition:background 0.35s;
    -webkit-user-select:none;
    -moz-user-select:none;
    -ms-user-select:none;
    user-select:none;
    
}

.joe_config__aside .tabs #config_custom:hover {
    background:#f2f6fc
}
.joe_config__aside .tabs #config_custom{
    width:33.33333333%;
    height:36px;
    line-height:36px;
    border-radius:18px
}


/*友链样式1*/
.clearfix {
    zoom:1;
    display:grid;
    height:auto; 
}
.readers-list {
    list-style:none;
    display:
    list-item;
}
.readers-list li {
    position:relative;
    padding:10px;
    float:left;
    margin-top:25px!important;
}
.readers-list li > a {
    border: 1px solid #eee;
    display: block;
    height: 85px;
    text-align: center;
    transition:all .5s;
    background: #f9f9f9;
}
.readers-list li>a:hover {
    border-color:#e5e5e5;
}
.readers-list a:hover em {
    top:45px;font-size:1em;
}
.readers-list a:hover span {
    opacity:1;left:50%;
}
.readers-list img {
    position:absolute;top:-30px;
    left:calc(50% - 30px);
    width:60px;
    height:60px;
    border-radius:50% ;
    box-shadow:0 0 6px 3px #0081B1;
    transition:all 1s;
    background:#fff;
}
.readers-list a:hover img {
	-webkit-transform:scale(.7);
	-moz-transform:scale(.7);
	-o-transform:scale(.7);
	-ms-transform:scale(.7);
	transform:scale(.7);
	border-radius: 0;
}
.readers-list em {
	position: absolute;
	top: 60px;
	-webkit-transform: translateX(-50%);
	left: 50%;
	font-style: normal;
	font-size: 1em;
	color: #818181;
	transition: all .3s;
	font-family: Helvetica,Arial,"PingFang SC","Microsoft YaHei","WenQuanYi Micro Hei","tohoma,sans-serif"
}
.readers-list span {
	position: absolute;
	left: 20%;
	bottom: 14px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	text-align: center;
	-webkit-transform: translateX(-50%);
	width: calc(100% - 30px);
	opacity: 0;
	transition:all .3s;
	color: #818181;
	font-family: Helvetica,Arial,"PingFang SC","Microsoft YaHei","WenQuanYi Micro Hei","tohoma,sans-serif"
}
@media(min-width:768px){
    .readers-list li{width:25%;}
}
@media(max-width:767px){
    .readers-list li{width:100%;height:100%;}
}















body,
.main,
.container {
  background: #f3f6f8;
}
.iconfont {
  color: #6b7386;
  padding-right: 4px;
}
.container .left-bar {
  position: fixed;
  padding: 0 20px;
  background: #30333c;
  color: #6b7386;
  box-sizing: border-box;
  flex-direction: column;
  height: 100vh;
  display: flex;
  width: 248px;
  transition: all .5s;
}
.container .main {
  position: relative;
  display: flex;
  flex-direction: column;
  -webkit-box-orient: vertical;
}
.container .main .iconfont {
  font-size: 18px;
  color: #000000;
}
.set-scroll {
  background-color: #494f5e;
  border-radius: 0;
  width: 4px;
}
.left-bar .title {
  color: white;
  font-size: 18px;
  height: 50px;
  line-height: 50px;
}
.left-bar .title p {
  text-align: center;
}
.left-bar .nav {
  flex: 1;
  -webkit-box-flex: 1;
  flex-grow: 1;
}
.left-bar .nav .item {
  height: 48px;
  line-height: 46px;
  border-top: 2px solid rgba(255, 255, 255, 0.04);
  border-bottom: 2px solid rgba(255, 255, 255, 0.04);
}
.left-bar .nav > .active {
  position: relative;
}
.left-bar .nav > .active .line {
  position: absolute;
  right: 0px;
  height: 30px;
  width: 3px;
  background: #3a85ff;
  z-index: 100000;
  margin-top: 10px;
}
.left-bar .nav-item {
  min-height: 100vh;
  overflow-y: scroll;
}
.left-bar .nav-item::-webkit-scrollbar-track {
  background-color: #494f5e;
  border-radius: 0;
  width: 4px;
}
.left-bar .nav-item::-webkit-scrollbar-thumb {
  background-color: #494f5e;
  border-radius: 0;
  width: 4px;
}
.left-bar .nav-item::-webkit-scrollbar {
  background-color: #494f5e;
  border-radius: 0;
  width: 4px;
}
.left-bar .nav-item li {
  padding-left: 20px;
  height: 32px;
  line-height: 32px;
}
.left-bar .nav-item li > .active,
.left-bar .nav-item li > .active i,
.left-bar .nav-item li:hover a,
.left-bar .nav-item li:hover i {
  color: #fff;
}
.left-bar .nav-item li a {
  text-decoration: none;
  font-size: 12px;
  display: inline-block;
  width: 100%;
  color: #6b7386;
}
.nav .item a {
  color: white;
}
.nav .comment {
  position: fixed;
  z-index: 100;
  bottom: 1px;
  width: 200px;
  background: #30333c;
}
.main #mainContent {
  max-width: 1200px;
}
.main .box {
  overflow: hidden;
  background: #fff;
  padding-bottom: 20px;
  filter: progid:DXImageTransform.Microsoft.Shadow(color=#909090,direction=120,strength=4);
  -moz-box-shadow: 2px 2px 10px #909090;
  -webkit-box-shadow: 2px 2px 10px #909090;
   box-shadow:2px 2px 10px #909090;
   margin-bottom: 20px;
   margin-top: 10px;
}
.main .box .sub-category > div {
  padding: 12px 0 0 2.1%;
  font-size: 18px;
}
.main .box .item {
  border: 1px solid #e4ecf3;
  box-shadow: 1px 2px 3px #f2f6f8;
  border-radius: 6px;
  background: #fff;
  padding: 10px;
  min-width: 223px;
  margin: 22px 0 0 2.1%;
  float: left;
  overflow: hidden;
  transition: all .3s;
}
.main .box .item:hover {
  transform: translateY(-5px);
}
.main .box .item .no-logo {
  color: #3273dc;
  font-weight: bold;
  font-size: 14px;
}
.main .box .item .logo {
  width: 100%;
  height: 40px;
  position: relative;
  font-size: 14px;
  font-weight: 700;
  color: #3273dc;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 .1rem;
}
.main .box .item .logo img {
  width: 45%;
  height: 40px;
}
.main .box .item .desc {
  color: gray;
  font-size: 12px;
  padding-top: 10px;
  height: 35px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.footer {
  width: 100%;
  bottom: 0;
  line-height: 45px;
  background: #fff;
  box-shadow: 0 0 1rem rgba(0, 0, 0, 0.2);
}
.footer .copyright {
  margin-left: 30px;
  color: #949494;
  font-size: 12px;
}
.footer .copyright a {
  text-decoration: none;
  color: #3a85ff;
}
#menu {
  display: none;
  width: 100%;
  background: #fff;
  padding: 10px 0;
  text-align: center;
}
#menu a {
  color: #3a85ff;
  font-size: 18px;
}
.download a,
.download i {
  color: #3668bd !important;
}
@media screen and (max-width: 481px) {
  .container .box .item {
    width: 39%;
    min-width: 100px;
  }
  .container .main {
    margin-left: 0;
  }
  .container .left-bar {
    z-index: 999;
    left: -249px;
  }
  #menu {
    display: block;
  }
}
@media screen and (min-width: 482px) {
  .container .box .item {
    width: 90%;
  }
  .container .left-bar {
    left: 0px;
  }
}
@media screen and (min-width: 790px) {
  .container .box .item {
    width: 43%;
  }
}
@media screen and (min-width: 1040px) {
  .container .box .item {
    width: 22.3%;
  }
}
@media screen and (min-width: 1200px) {
  .container .box .item {
    width: 22.3%;
  }
}

.joe_config__link{display:none;margin-left:15px;background:#fff;padding:15px;flex:1;box-shadow:0px 0px 20px -5px rgba(158,158,158,0.22);border-radius:8px;line-height:28px;color:#606266}

.imgUpload .imgUpload_btn {
    text-align:right;
}

.imgUpload .imgUpload_btn span {
    cursor: pointer;
}
html[data-night='night'] #loveSitetime4Aside {
    color: var(--routine)!important;
}
html[data-night='night'] #loveSitetime4Header {
    color: var(--routine)!important;
}

.joe_post {
	position: relative;
}

.joe_post .j-floor {
	position: absolute;
	top: 0;
	bottom: 0;
	left: -5px;
	-webkit-transform: translateX(-100%);
	transform: translateX(-100%)
}

.joe_post .j-floor .contain {
	position: -webkit-sticky;
	position: sticky;
	transition: top 0.5s;
	background: var(--background);
	box-shadow: var(--box_shadow);
	padding: 15px;
	border-radius: var(--radius-pc);
	width: 200px;
	max-height: 444px;
	overflow-y: auto;
	-webkit-overflow-scrolling: touch
}

.joe_post .j-floor .contain .title {
	font-weight: 700;
	color: var(--main);
	margin-bottom: 15px;
	font-size: 15px;
	line-height: 20px
}

.joe_post .j-floor .contain ul li ul {
	padding-left: 2em
}

.joe_post .j-floor .contain ul li ul ul {
	padding-left: 0
}

.joe_post .j-floor .contain ul li a {
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	cursor: pointer;
	padding-left: 5px;
	display: block;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	color: var(--routine);
	line-height: 32px
}

.joe_post .j-floor .contain ul li a:hover {
	color: var(--theme)
}

.joe_post .j-floor .contain ul li.visible>a {
	color: var(--theme)
}

.joe_post .j-floor .contain .toc-marker {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none
}

.joe_post .j-floor .contain .toc-marker path {
	transition: all 0.35s
}














































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































