<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 开始计时
$startTime = microtime(true);

// 连接数据库
$searchConn = new mysqli("localhost", "sgksjk", "sgksjk", "sgksjk");
if ($searchConn->connect_error) {
    echo json_encode([
        'code' => 500,
        'message' => '数据库连接失败'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 定义表和字段映射
    $tables = [
        
    "chphone" => ["idcard" => "身份证", "phone" => "电话", "name" => "姓名"],
    "aurora独家数据" => ["idcard" => "身份证", "phone" => "手机号", "name" => "姓名"],
];

// 随机选择一个表
$table = array_rand($tables);
$indices = $tables[$table];

// 使用索引字段进行查询
$idcardField = $indices['idcard'];
$nameField = $indices['name'];

// 生成一个随机数（假设表中有100万条记录）
$randomId = mt_rand(1, 5000);

// 使用索引直接获取记录
$sql = "SELECT `$idcardField` as idcard, `$nameField` as name 
        FROM `$table` 
        WHERE `$idcardField` IS NOT NULL 
        AND `$nameField` IS NOT NULL 
        AND `$idcardField` != '' 
        AND `$nameField` != ''
        LIMIT 1 OFFSET $randomId";

$result = $searchConn->query($sql);

if ($result && $result->num_rows > 0) {
    $data = $result->fetch_assoc();
    $executionTime = round(microtime(true) - $startTime, 4);
    echo json_encode([
        'code' => 200,
        'message' => 'Ok',
        'data' => [
            'name' => $data['name'],
            'idcard' => $data['idcard']
        ],
    ], JSON_UNESCAPED_UNICODE);
} else {
    $executionTime = round(microtime(true) - $startTime, 4);
    echo json_encode([
        'code' => 404,
        'message' => 'Sorry.',
    ], JSON_UNESCAPED_UNICODE);
}

$searchConn->close();
?> 