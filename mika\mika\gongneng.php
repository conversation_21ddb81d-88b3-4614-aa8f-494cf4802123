<?php
require 'vendor/autoload.php';
require 'address.php';

use Jxlwqq\IdValidator\IdValidator;

// 允许跨域访问
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json; charset=utf-8');

// 获取参数
$name = $_GET['name'] ?? '';
$idcard = $_GET['idcard'] ?? '';
$key = $_GET['key'] ?? '';

// 验证key和积分
$userFile = 'user.json';
$userData = [];
if (file_exists($userFile)) {
    $userData = json_decode(file_get_contents($userFile), true) ?? [];
}

// 检查key是否存在且积分是否足够
if (empty($key) || !isset($userData['users'][$key])) {
    echo json_encode([
        'code' => 403,
        'message' => '无效的用户key',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

if ($userData['users'][$key]['balance'] < 12) {
    echo json_encode([
        'code' => 403,
        'message' => '积分不足，需要12积分',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 扣除积分
$userData['users'][$key]['balance'] -= 12;
file_put_contents($userFile, json_encode($userData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

// 验证参数
if (empty($name) || empty($idcard)) {
    echo json_encode([
        'code' => 400,
        'message' => '姓名和身份证号不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 初始化身份证验证器
    $idValidator = new IdValidator();
    
    // 验证身份证号
    if (!$idValidator->isValid($idcard)) {
        echo json_encode([
            'code' => 400,
            'message' => '身份证号格式不正确',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 获取身份证信息
    $info = $idValidator->getInfo($idcard);
    
    // 检查是否存在持久化数据
    $persistFile = 'persist_data.json';
    $persistData = [];
    if (file_exists($persistFile)) {
        $persistData = json_decode(file_get_contents($persistFile), true) ?? [];
    }
    
    // 如果该身份证号已有记录，使用已存在的数据
    if (isset($persistData[$idcard])) {
        $fakeAddress = $persistData[$idcard]['fake_address'];
        $randomAvatar = $persistData[$idcard]['avatar'];
    } else {
        // 生成新的假地址和头像
        $fakeAddress = generateAddress($idcard);
        $sex = $info['sex'] === 1 ? '男' : '女';
        $genderDir = $sex === '男' ? 'dt/男/' : 'dt/女/';
        $avatars = glob($genderDir . '*.jpg');
        $randomAvatar = $avatars[array_rand($avatars)];
        
        // 保存新生成的数据
        $persistData[$idcard] = [
            'fake_address' => $fakeAddress,
            'avatar' => $randomAvatar
        ];
        file_put_contents($persistFile, json_encode($persistData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
    }
    
    // 获取真实省市区地址和其他信息
    $realAddress = $info['address'];
    $birthdayCode = $info['birthdayCode'];
    $sex = $info['sex'] === 1 ? '男' : '女';
    
    // 读取背景图片和头像
    $image = imagecreatefrompng('bg.png');
    $avatar = imagecreatefromjpeg($randomAvatar);
    
    // 设置头像大小和位置
    $avatarWidth = 255;
    $avatarHeight = 320;
    $avatarX = 20;
    $avatarY = 20;
    
    // 将头像绘制到背景图上
    imagecopyresampled($image, $avatar, $avatarX, $avatarY, 0, 0, $avatarWidth, $avatarHeight, imagesx($avatar), imagesy($avatar));
    imagedestroy($avatar);
    
    // 设置字体颜色
    $black = imagecolorallocate($image, 0, 0, 0);
    
    // 设置字体路径
    $font = __DIR__ . '/fonts/simhei.ttf';
    $fontSize = 16;
    
    // 在图片上写入信息
    imagettftext($image, $fontSize, 0, 470, 45, $black, $font, $name);
    imagettftext($image, $fontSize, 0, 290, 385, $black, $font, $idcard);
    imagettftext($image, $fontSize, 0, 470, 335, $black, $font, $fakeAddress);
    imagettftext($image, $fontSize, 0, 470, 285, $black, $font, $realAddress);
    imagettftext($image, $fontSize, 0, 470, 140, $black, $font, $birthdayCode);
    imagettftext($image, $fontSize, 0, 470, 90, $black, $font, $sex);
    imagettftext($image, $fontSize, 0, 470, 190, $black, $font, "中国");
    imagettftext($image, $fontSize, 0, 1050, 90, $black, $font, "汉");
    
    // 保存图片
    $outputPath = 'output_' . time() . '.png';
    imagepng($image, $outputPath);
    imagedestroy($image);
    
    echo json_encode([
        'code' => 200,
        'message' => 'success',
        'data' => [
            'name' => $name,
            'idcard' => $idcard,
            'fake_address' => $fakeAddress,
            'real_address' => $realAddress,
            'image_url' => "https://mika.qnm6.top/".$outputPath
        ]
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}