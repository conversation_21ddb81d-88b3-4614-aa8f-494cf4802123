<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小媛の业务表</title>
    <link href="https://fonts.googleapis.com/css2?family=ZCOOL+KuaiLe&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f8e1ff 0%, #c1eaff 100%);
            font-family: 'ZCOOL KuaiLe', '微软雅黑', 'Arial', sans-serif;
            color: #4b3f72;
            margin: 0;
            padding: 0;
            /* 横屏适配 */
            min-width: 1200px;
            min-height: 100vh;
            overflow-x: auto;
        }
        .container {
            max-width: none;
            width: 90vw;
            min-width: 1100px;
            margin: 40px auto;
            background: rgba(255,255,255,0.95);
            border-radius: 18px;
            box-shadow: 0 8px 32px rgba(120, 80, 200, 0.18);
            padding: 32px 24px 24px 24px;
            position: relative;
            display: flex;
            flex-direction: row;
            gap: 40px;
            align-items: flex-start;
            justify-content: center;
            min-height: 600px;
        }
        .left-panel, .right-panel {
            flex: 1 1 0;
            min-width: 420px;
            display: flex;
            flex-direction: column;
            justify-content: stretch;
            height: 100%;
        }
        .left-panel {
            align-items: flex-end;
        }
        .right-panel {
            align-items: flex-start;
        }
        h1 {
            text-align: center;
            font-size: 2.6em;
            margin-bottom: 10px;
            color: #ff6ac1;
            letter-spacing: 2px;
            text-shadow: 0 2px 8px #fff0fa;
            margin-top: 0;
        }
        .main-title {
            margin-top: 40px;
            margin-bottom: 0;
            font-size: 3em;
            color: #ff6ac1;
            text-shadow: 0 2px 12px #fff0fa;
            letter-spacing: 3px;
            font-family: 'ZCOOL KuaiLe', '微软雅黑', 'Arial', sans-serif;
        }
        h2 {
            color: #6a8dff;
            margin-top: 32px;
            margin-bottom: 12px;
            font-size: 1.5em;
            border-left: 6px solid #ffb6b9;
            padding-left: 10px;
        }
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: #fff8fc;
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 24px;
            box-shadow: 0 2px 12px #f3e6ff;
        }
        th, td {
            padding: 14px 10px;
            text-align: center;
            font-size: 1.1em;
        }
        th {
            background: #ffe3f7;
            color: #ff6ac1;
            font-weight: bold;
        }
        tr:nth-child(even) td {
            background: #f3f8ff;
        }
        tr:hover td {
            background: #e6f7ff;
            transition: 0.2s;
        }
        .anime-deco {
            position: absolute;
            top: -40px;
            right: -30px;
            width: 120px;
            opacity: 0.7;
            pointer-events: none;
        }
        .anime-star {
            position: absolute;
            left: -30px;
            bottom: -30px;
            width: 80px;
            opacity: 0.6;
            pointer-events: none;
        }
        .feature-list {
            background: #f7faff;
            border-radius: 10px;
            padding: 16px 18px;
            margin-top: 10px;
            font-size: 1.08em;
            color: #5a4e7c;
            box-shadow: 0 1px 6px #e0eaff;
        }
        .feature-list span {
            display: inline-block;
            background: #ffe3f7;
            color: #ff6ac1;
            border-radius: 6px;
            padding: 3px 10px;
            margin: 4px 6px 4px 0;
            font-size: 0.98em;
        }
        @media (max-width: 1100px) {
            .container {
                flex-direction: column;
                width: 98vw;
                min-width: unset;
            }
            .left-panel, .right-panel {
                min-width: unset;
            }
        }
        .main-title .yanwenzi {
            font-size: 0.7em;
            margin-left: 10px;
            color: #ff6ac1;
            font-family: 'ZCOOL KuaiLe', '微软雅黑', 'Arial', sans-serif;
        }
        .qwq-icon {
            width: 32px;
            height: 32px;
            vertical-align: middle;
            margin: 0 4px 0 2px;
        }
        h2 .yanwenzi {
            font-size: 0.7em;
            margin-left: 6px;
            color: #6a8dff;
            font-family: 'ZCOOL KuaiLe', '微软雅黑', 'Arial', sans-serif;
        }
        .feature-list .yanwenzi {
            font-size: 0.9em;
            margin-left: 8px;
            color: #ffb6b9;
            font-family: 'ZCOOL KuaiLe', '微软雅黑', 'Arial', sans-serif;
        }
        .svg-icon {
            display: inline-block;
            vertical-align: middle;
            margin: 0 4px 0 2px;
        }
        .qq-logo svg {
            filter: drop-shadow(0 2px 6px #b3e6ff);
        }
        .github-logo svg {
            filter: drop-shadow(0 2px 6px #bbb);
        }
    </style>
</head>
<body>
    <h1 class="main-title">小媛の业务表 <span class="yanwenzi">(｡･ω･｡)ﾉ♡</span></h1>
    <div class="container">
        <div class="left-panel">
            <h2>QQ会员区 <span class="svg-icon qq-logo"> 
                <svg width="28" height="28" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="512" cy="512" r="512" fill="#50B5FF"/>
                  <ellipse cx="512" cy="600" rx="220" ry="180" fill="#fff"/>
                  <ellipse cx="512" cy="480" rx="160" ry="140" fill="#fff"/>
                  <ellipse cx="420" cy="470" rx="30" ry="40" fill="#222"/>
                  <ellipse cx="604" cy="470" rx="30" ry="40" fill="#222"/>
                  <ellipse cx="512" cy="650" rx="60" ry="30" fill="#F9B7B7"/>
                </svg>
            </span><span class="yanwenzi">(≧∇≦)ﾉ</span></h2>
            <table>
                <tr>
                    <th>类型</th>
                    <th>月</th>
                    <th>季</th>
                    <th>年</th>
                </tr>
                <tr>
                    <td>Vip</td>
                    <td>6.6</td>
                    <td>19.8</td>
                    <td>75.2</td>
                </tr>
                <tr>
                    <td>Svip</td>
                    <td>13.14</td>
                    <td>33.44</td>
                    <td>78.88</td>
                </tr>
                <tr>
                    <td>黄钻</td>
                    <td>7.2</td>
                    <td>19.8</td>
                    <td>75.2</td>
                </tr>
                <tr>
                    <td>豪华黄钻</td>
                    <td>9.4</td>
                    <td>27.6</td>
                    <td>108.8</td>
                </tr>
                <tr>
                    <td>大会员<br><span style="font-size:0.9em;color:#ff6ac1;">赠送Svip/月+豪华黄钻/月</span></td>
                    <td>23.4</td>
                    <td>69.8</td>
                    <td>275.8</td>
                </tr>
                <tr>
                    <td>绿钻</td>
                    <td>12.8<br><span style="font-size:0.9em;color:#6a8dff;">4.8/周</span></td>
                    <td>36.6</td>
                    <td>148.2</td>
                </tr>
                <tr>
                    <td>豪华绿钻</td>
                    <td>22.34</td>
                    <td>-</td>
                    <td>192.12</td>
                </tr>
                <tr>
                    <td>腾讯vip</td>
                    <td>16.6<br><span style="font-size:0.9em;color:#6a8dff;">8.8/周</span></td>
                    <td>46.6</td>
                    <td>146.6</td>
                </tr>
                <tr>
                    <td>腾讯Svip</td>
                    <td>25.8<br><span style="font-size:0.9em;color:#6a8dff;">15.8/周</span></td>
                    <td>69.8</td>
                    <td>246.6</td>
                </tr>
            </table>
        </div>
        <div class="right-panel">
            <h2>社工会员区 <span class="svg-icon github-logo">
                <svg width="28" height="28" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="512" cy="512" r="512" fill="#181717"/>
                  <path d="M512 180c-183 0-332 149-332 332 0 147 95 272 226 316 16 3 22-7 22-16v-56c-92 20-112-44-112-44-15-39-36-49-36-49-30-21 2-21 2-21 33 2 50 34 50 34 29 50 76 36 94 28 3-21 11-36 20-44-73-8-150-36-150-160 0-35 12-64 33-87-3-8-14-41 3-85 0 0 27-9 88 33 26-7 54-11 82-11s56 4 82 11c61-42 88-33 88-33 17 44 6 77 3 85 21 23 33 52 33 87 0 124-77 152-150 160 11 9 21 27 21 55v81c0 9 6 19 22 16 131-44 226-169 226-316 0-183-149-332-332-332z" fill="#fff"/>
                </svg>
            </span><span class="yanwenzi">(๑•̀ㅂ•́)و✧</span></h2>
            <table>
                <tr>
                    <th>套餐</th>
                    <th>价格</th>
                    <th>功能/数据占比</th>
                </tr>
                <tr>
                    <td>月卡</td>
                    <td>13.14</td>
                    <td>免费+部分付费功能<br>数据占比:20%</td>
                </tr>
                <tr>
                    <td>季卡</td>
                    <td>33.44</td>
                    <td>免费+所有付费功能<br>数据占比:50%</td>
                </tr>
                <tr>
                    <td>年卡</td>
                    <td>113.23</td>
                    <td>免费+所有付费功能<br>数据占比:80%</td>
                </tr>
                <tr>
                    <td>永久卡</td>
                    <td>167.99</td>
                    <td>免费+所有付费功能<br>数据占比:100%</td>
                </tr>
            </table>
            <div class="feature-list">
                <strong>功能列表：</strong><span class="yanwenzi">(つ✧ω✧)つ</span><br>
                <span>Q绑查询</span>
                <span>身份证正反面获取</span>
                <span>Q绑反查</span>
                <span>网红猎魔</span>
                <span>二维码生成</span>
                <span>白底个户</span>
                <span>IP查询</span>
                <span>身份证补齐</span>
                <span>LOL信息查询</span>
                <span>综合社工查询</span>
                <span>LOL昵称查询</span>
                <span>地区猎魔</span>
                <span>微博ID查询</span>
                <span>二要素核验</span>
                <span>微博反查</span>
                <span>档案个户</span>
                <span>淘宝买家秀</span>
                <span>空号检测</span>
                <span>每日自律</span>
                <span>姓名猎魔</span>
                <span>卡泡聆听</span>
                <span>等8大独家功能</span>
            </div>
        </div>
        <img src="https://img.alicdn.com/imgextra/i4/O1CN01QwQw2B1QwQwQwQwQw_!!6000000000000-2-tps-400-400.png" class="anime-deco" alt="二次元女孩装饰" />
        <img src="https://img.alicdn.com/imgextra/i2/O1CN01QwQw2B1QwQwQwQwQw_!!6000000000000-2-tps-200-200.png" class="anime-star" alt="二次元星星装饰" />
    </div>
</body>
</html> 