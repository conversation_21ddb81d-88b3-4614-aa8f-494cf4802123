<?php
use Jxlwqq\IdValidator\IdValidator;

// 生成户号
function generateHouseNumber() {
    $floor = rand(1, 9);  // 楼层（1-9层）
    $room = rand(1, 3);   // 房间号（1-3号）
    return $floor . "0" . $room;
}

// 生成地址
function generateAddress($idNumber) {
    $idValidator = new IdValidator();
    $info = $idValidator->getInfo($idNumber);
    if (!$info) {
        return null;
    }
    
// 定义区域数组
$districts = [
    "东城区","西城区","朝阳区","丰台区","石景山区","海淀区","门头沟区","房山区","通州区","顺义区","昌平区","大兴区","怀柔区","平谷区","密云区","延庆区","和平区","河东区","河西区","南开区","河北区","红桥区","东丽区","西青区","津南区","北辰区","武清区","宝坻区","滨海新区","宁河区","静海区","蓟州区","长安区","桥西区","新华区","井陉矿区","裕华区","藁城区","鹿泉区","栾城区","井陉县","正定县","行唐县","灵寿县","高邑县","深泽县","赞皇县","无极县","平山县","元氏县","赵县","石家庄高新技术产业开发区","石家庄循环化工园区","辛集市","晋州市","新乐市","小店区","迎泽区","杏花岭区","尖草坪区","万柏林区","晋源区","清徐县","阳曲县","娄烦县","山西转型综合改革示范区","古交市","新城区","回民区","玉泉区","赛罕区","土默特左旗","托克托县","和林格尔县","清水河县","武川县","呼和浩特经济技术开发区","和平区","沈河区","大东区","皇姑区","铁西区","苏家屯区","浑南区","沈北新区","于洪区","辽中区","康平县","法库县","新民市","南关区","宽城区","朝阳区","二道区","绿园区","双阳区","九台区","农安县","长春经济技术开发区","长春净月高新技术产业开发区","长春高新技术产业开发区","长春汽车经济技术开发区","榆树市","德惠市","公主岭市","道里区","南岗区","道外区","平房区","松北区","香坊区","呼兰区","阿城区","双城区","依兰县","方正县","宾县","巴彦县","木兰县","通河县","延寿县","尚志市","五常市","黄浦区","徐汇区","长宁区","静安区","普陀区","虹口区","杨浦区","闵行区","宝山区","嘉定区","浦东新区","金山区","松江区","青浦区","奉贤区","崇明区","玄武区","秦淮区","建邺区","鼓楼区","浦口区","栖霞区","雨花台区","江宁区","六合区","溧水区","高淳区","上城区","拱墅区","西湖区","滨江区","萧山区","余杭区","富阳区","临安区","临平区","钱塘区","桐庐县","淳安县","建德市","瑶海区","庐阳区","蜀山区","包河区","长丰县","肥东县","肥西县","庐江县","合肥高新技术产业开发区","合肥经济技术开发区","合肥新站高新技术产业开发区","巢湖市","鼓楼区","台江区","仓山区","马尾区","晋安区","长乐区","闽侯县","连江县","罗源县","闽清县","永泰县","平潭县","福清市","东湖区","西湖区","青云谱区","青山湖区","新建区","红谷滩区","南昌县","安义县","进贤县","历下区","市中区","槐荫区","天桥区","历城区","长清区","章丘区","济阳区","莱芜区","钢城区","平阴县","商河县","济南高新技术产业开发区","中原区","二七区","管城回族区","金水区","上街区","惠济区","中牟县","郑州经济技术开发区","郑州高新技术产业开发区","郑州航空港经济综合实验区","巩义市","荥阳市","新密市","新郑市","登封市","江岸区","江汉区","硚口区","汉阳区","武昌区","青山区","洪山区","东西湖区","汉南区","蔡甸区","江夏区","黄陂区","新洲区","芙蓉区","天心区","岳麓区","开福区","雨花区","望城区","长沙县","浏阳市","宁乡市","荔湾区","越秀区","海珠区","天河区","白云区","黄埔区","番禺区","花都区","南沙区","从化区","增城区","兴宁区","青秀区","江南区","西乡塘区","良庆区","邕宁区","武鸣区","隆安县","马山县","上林县","宾阳县","横州市","秀英区","龙华区","琼山区","美兰区","万州区","涪陵区","渝中区","大渡口区","江北区","沙坪坝区","九龙坡区","南岸区","北碚区","綦江区","大足区","渝北区","巴南区","黔江区","长寿区","江津区","合川区","永川区","南川区","璧山区","铜梁区","潼南区","荣昌区","开州区","梁平区","武隆区","锦江区","青羊区","金牛区","武侯区","成华区","龙泉驿区","青白江区","新都区","温江区","双流区","郫都区","新津区","金堂县","大邑县","蒲江县","都江堰市","彭州市","邛崃市","崇州市","简阳市","南明区","云岩区","花溪区","乌当区","白云区","观山湖区","开阳县","息烽县","修文县","清镇市","五华区","盘龙区","官渡区","西山区","东川区","呈贡区","晋宁区","富民县","宜良县","石林彝族自治县","嵩明县","禄劝彝族苗族自治县","寻甸回族彝族自治县","安宁市","城关区","堆龙德庆区","达孜区","林周县","当雄县","尼木县","曲水县","墨竹工卡县","格尔木藏青工业园区","拉萨经济技术开发区","西藏文化旅游创意园区","达孜工业园区","新城区","碑林区","莲湖区","灞桥区","未央区","雁塔区","阎良区","临潼区","长安区","高陵区","鄠邑区","蓝田县","周至县","城关区","七里河区","西固区","安宁区","红古区","永登县","皋兰县","榆中县","兰州新区","城东区","城中区","城西区","城北区","湟中区","大通回族土族自治县","湟源县","兴庆区","西夏区","金凤区","永宁县","贺兰县","灵武市","天山区","沙依巴克区","新市区","水磨沟区","头屯河区","达坂城区","米东区","乌鲁木齐县"
];

// 定义路名数组
$roads = [
    "朝阳路", "解放路", "人民路", "建设路", "和平路", "中山路", "南京路", "延安路", "胜利路",
    "新华路", "文化路", "团结路", "光明路", "友谊路", "红旗路", "黄河路", "长江路", "滨海路",
    "世纪大道", "中央大街", "公园路", "学院路", "科技路", "工业路", "商业路", "幸福路", "和谐路",
    "东华门街道","景山街道","交道口街道","安定门街道","北新桥街道","东四街道","朝阳门街道","建国门街道",
    "东直门街道","和平里街道","前门街道","崇文门外街道","东花市街道","龙潭街道","体育馆路街道","天坛街道",
    "永定门外街道","西长安街街道","新街口街道","月坛街道","展览路街道","德胜街道","金融街街道","什刹海街道",
    "大栅栏街道","天桥街道","椿树街道","陶然亭街道","广安门内街道","牛街街道","白纸坊街道","广安门外街道",
    "建外街道","朝外街道","呼家楼街道","三里屯街道","左家庄街道","香河园街道","和平街街道","安贞街道","亚运村街道",
    "小关街道","酒仙桥街道","麦子店街道","团结湖街道","六里屯街道","八里庄街道","双井街道","劲松街道","潘家园街道",
        "垡头街道","首都机场街道","南磨房地区","高碑店地区","将台地区","太阳宫地区","大屯街道","望京街道","小红门地区"
];

// 定义小区名数组
$communities = [
    "安阳小区", "阳光小区", "幸福小区", "平安小区", "和谐小区", "金域华府", "碧桂园", "万科城",
    "绿地国际", "保利花园", "中海名城", "华润置地", "恒大绿洲", "龙湖天街", "融创公馆", "世茂滨江",
    "绿城百合", "金科天籁", "蓝光公园", "富力城", "雅居乐", "星河湾", "招商花园", "保利国际", "中海寰宇"
];

// 定义村名数组
$villages = [
    "百花村", "幸福村", "和平村", "光明村", "和谐村", "红旗村", "东风村", "胜利村", "团结村",
    "友谊村", "前进村", "红星村", "朝阳村", "解放村", "新华村", "文化村", "建设村", "富强村",
    "民主村", "文明村", "振兴村", "丰收村", "青山村", "绿水村", "阳光村", "金田村", "银花村"
];

    $fuzzyAddress = $info['address'];
    $district = $districts[array_rand($districts)];
    
    // 随机选择一种地址格式
    $formatType = rand(1, 3);
    
    if ($formatType == 1) {
        $road = $roads[array_rand($roads)];
        $street = ["大街", "大道", "路"][array_rand(["大街", "大道", "路"])];
        $building = rand(1, 100);
        return $fuzzyAddress . $district . $road . $street . $building . "栋";
    } elseif ($formatType == 2) {
        $village = $villages[array_rand($villages)];
        $number = rand(1, 100);
        $room = generateHouseNumber();
        return $fuzzyAddress . $district . $village . $number . "号" . $room . "室";
    } else {
        $road = $roads[array_rand($roads)];
        $community = $communities[array_rand($communities)];
        $building = rand(1, 20);
        $unit = rand(1, 4);
        $household = generateHouseNumber();
        return $fuzzyAddress . $district . $road . $community . $building . "栋" . $unit . "单元" . $household . "户";
    }
}