<?php
// 引入数据库配置文件
require '../db.php';

// 获取动态参数 code
if (isset($_GET['code'])) {
    $code = $_GET['code'];

    // 检查 code 是否为0或1
    if ($code !== '0' && $code !== '1') {
        echo json_encode(["code" => 400, "message" => "请输入正确的code（0或1）"], JSON_UNESCAPED_UNICODE);
        exit();
    }

    // 准备SQL语句，根据code取已使用或未使用的卡密列表
    $stmt = $mysqli->prepare("SELECT kamitime, kami, oktoken, oktime FROM kamis WHERE kamicode = ?");
    
    if (!$stmt) {
        echo json_encode(["code" => 500, "message" => "SQL语句准备失败: " . $mysqli->error], JSON_UNESCAPED_UNICODE);
        exit();
    }

    // 绑定参数并执行查询
    $stmt->bind_param("s", $code);
    $stmt->execute();
    $result = $stmt->get_result();

    // 初始化卡密列表数组
    $kamis = [];

    // 遍历查询结果
    while ($row = $result->fetch_assoc()) {
        $kamis[] = $row;
    }

    // 返回查询结果
    echo json_encode(["code" => 200, "message" => "卡密列表获取成功", "kamis" => $kamis], JSON_UNESCAPED_UNICODE);

    $stmt->close();
} else {
    // 参数缺失
    echo json_encode(["code" => 400, "message" => "请输入code参数"], JSON_UNESCAPED_UNICODE);
}

$mysqli->close();
?>
