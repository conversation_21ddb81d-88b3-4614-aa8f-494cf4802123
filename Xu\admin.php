<?php
header('Content-Type: application/json; charset=utf-8');

// 获取用户数据
$userData = json_decode(file_get_contents('user.json'), true);

// 处理不同的管理操作
$action = $_GET['action'] ?? '';

switch ($action) {
    case 'list_users':
        // 列出所有用户
        $users = [];
        foreach ($userData['users'] as $username => $user) {
            $users[] = [
                'username' => $username,
                'token' => $user['token'],
                'queries_left' => $user['queries_left']
            ];
        }
        echo json_encode(['code' => 200, 'users' => $users]);
        break;

    case 'add_user':
        // 添加新用户
        $data = json_decode(file_get_contents('php://input'), true);
        if (!isset($data['username']) || !isset($data['queries'])) {
            echo json_encode(['code' => 400, 'message' => '参数不完整']);
            exit;
        }
        
        $username = $data['username'];
        $queries = (int)$data['queries'];
        $token = bin2hex(random_bytes(16)); // 生成随机token
        
        $userData['users'][$username] = [
            'token' => $token,
            'queries_left' => $queries
        ];
        
        file_put_contents('user.json', json_encode($userData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo json_encode(['code' => 200, 'message' => '用户添加成功', 'token' => $token]);
        break;

    case 'recharge':
        // 充值查询次数
        $data = json_decode(file_get_contents('php://input'), true);
        if (!isset($data['username']) || !isset($data['queries'])) {
            echo json_encode(['code' => 400, 'message' => '参数不完整']);
            exit;
        }
        
        $username = $data['username'];
        $queries = (int)$data['queries'];
        
        if (!isset($userData['users'][$username])) {
            echo json_encode(['code' => 404, 'message' => '用户不存在']);
            exit;
        }
        
        $userData['users'][$username]['queries_left'] += $queries;
        file_put_contents('user.json', json_encode($userData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo json_encode(['code' => 200, 'message' => '充值成功']);
        break;

    case 'delete_user':
        // 删除用户
        $data = json_decode(file_get_contents('php://input'), true);
        if (!isset($data['username'])) {
            echo json_encode(['code' => 400, 'message' => '参数不完整']);
            exit;
        }
        
        $username = $data['username'];
        if (!isset($userData['users'][$username])) {
            echo json_encode(['code' => 404, 'message' => '用户不存在']);
            exit;
        }
        
        unset($userData['users'][$username]);
        file_put_contents('user.json', json_encode($userData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo json_encode(['code' => 200, 'message' => '用户删除成功']);
        break;

    default:
        echo json_encode(['code' => 400, 'message' => '未知操作']);
        break;
}
?>
