<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');
include './verify_vip.php';

// 获取请求中的token
$token = $_GET['token'];  // 或者通过其他方式获取

// 自定义限制（可以自由组合）
$vipTimeLimit = false;  // 是否启用会员时间限制
$vipCodeLimit = true;  // 是否启用会员功能限制

// 定义回调函数
$callback = function($vipcode, $viptime) use ($vipTimeLimit, $vipCodeLimit) {
    if ($vipCodeLimit) {
        // 如果启用会员功能限制，执行相应的检查
        $result = vipCodeLimit($vipcode);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    if ($vipTimeLimit) {
        // 如果启用会员时间限制，执行相应的检查
        $result = vipTimeLimit($viptime);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    return true;  // 如果没有任何限制失败，返回true
};

// 调用验证函数
$verificationResult = verifyVipStatus($token, $callback);

// 如果返回的是错误信息，则输出
if ($verificationResult !== true) {
    echo json_encode($verificationResult, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

/**
 * 查询本地数据库
 */
function queryLocalDatabase($qq) {
    // 连接数据库
    $servername = "localhost";
    $username = "sgksjk";
    $password = "sgksjk";
    $dbname = "sgksjk";

    // 创建连接
    $conn = new mysqli($servername, $username, $password, $dbname);

    // 检查连接
    if ($conn->connect_error) {
        return [
            'found' => false,
            'data' => "QQ: $qq\n数据库连接失败\n"
        ];
    }

    // 查询数据库
    $sql = "SELECT `手机号` FROM `Q绑数据` WHERE `QQ号` = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $qq);

    $stmt->execute();
    $result = $stmt->get_result();

    // 判断数据库中是否存在该QQ号
    if ($result->num_rows > 0) {
        // 如果查询到了数据
        $row = $result->fetch_assoc();
        $phone = $row['手机号'];
        $shuju = "QQ: $qq\n查询到以下结果↓:\n\n";
        $shuju .= "qq: $qq\n";
        $shuju .= "手机号: " . $phone . "\n";

        $stmt->close();
        $conn->close();

        return [
            'found' => true,
            'data' => $shuju
        ];
    } else {
        // 如果没有查询到数据
        $stmt->close();
        $conn->close();

        return [
            'found' => false,
            'data' => "QQ: $qq\n本地库中无记录\n"
        ];
    }
}

/**
 * 调用第三方API查询QQ绑定手机号
 */
function callThirdPartyAPI($qq) {
    $api_url = 'https://avsov.com/qq_tel';

    // 准备POST数据
    $post_data = array(
        'qq' => $qq
    );

    // 初始化cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/x-www-form-urlencoded',
        'Accept: application/json',
        'X-Requested-With: XMLHttpRequest'
    ));

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);

    if ($response === false || !empty($curl_error)) {
        return [
            'success' => false,
            'error' => '第三方API调用失败: ' . ($curl_error ?: 'Unknown error')
        ];
    }

    if ($http_code !== 200) {
        return [
            'success' => false,
            'error' => '第三方API返回错误状态码: ' . $http_code
        ];
    }

    $data = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        return [
            'success' => false,
            'error' => '第三方API返回数据格式错误: ' . json_last_error_msg()
        ];
    }

    return [
        'success' => true,
        'data' => $data
    ];
}

$startTime = microtime(true);
// 获取动态参数qq
$qq = isset($_GET['qq']) ? $_GET['qq'] : '';

// 判断qq是否符合要求（大于4位小于14位的纯数字）
if (preg_match('/^\d{5,13}$/', $qq)) {

    // 首先尝试调用第三方API
    $api_result = callThirdPartyAPI($qq);

    if ($api_result['success']) {
        $api_data = $api_result['data'];

        // 检查第三方API返回的数据格式
        if (isset($api_data['code']) && $api_data['code'] == 200 && isset($api_data['p'])) {
            // 第三方API查询成功
            $phone = $api_data['p'];
            $shuju = "QQ: $qq\n查询到以下结果↓:\n\n";
            $shuju .= "qq: $qq\n";
            $shuju .= "手机号: " . $phone . "\n";

            $message = '查询成功，更多接口在频道@idatas8';
            $code = 200;
        } else {
            // 第三方API无数据，尝试本地数据库
            $local_result = queryLocalDatabase($qq);
            if ($local_result['found']) {
                $shuju = $local_result['data'];
                $message = '查询成功，更多接口在频道@idatas8';
                $code = 200;
            } else {
                $shuju = "QQ: $qq\n暂无相关记录\n";
                $message = '未找到相关记录';
                $code = 404;
            }
        }
    } else {
        // 第三方API调用失败，尝试本地数据库
        $local_result = queryLocalDatabase($qq);
        if ($local_result['found']) {
            $shuju = $local_result['data'];
            $message = '查询成功，更多接口在频道@idatas8';
            $code = 200;
        } else {
            $shuju = "QQ: $qq\n查询失败: " . $api_result['error'] . "\n";
            $message = '查询失败，请稍后重试';
            $code = 500;
        }
    }

    // 获取请求结束时间并计算执行时间
    $executionTime = round(microtime(true) - $startTime, 4);

    // 输出结果
    echo json_encode([
        'code' => $code,
        'message' => $message,
        'shuju' => $shuju,
        'execution_time' => $executionTime . ' 秒',
        '频道' => "@idatas8",
        '官网' => '【及时更新,更多接口】https://api.qnm6.top/'
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

} else {
    // 如果qq不符合条件，输出错误信息
    echo json_encode([
        'code' => 400,
        'message' => '请输入有效的QQ号（5到13位数字）',
        '频道' => "@idatas8",
        '官网' => '【及时更新,更多接口】https://api.qnm6.top/'
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

?>
