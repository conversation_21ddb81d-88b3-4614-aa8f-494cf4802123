import requests

def generate_kami(days, count):
    url = f"http://127.0.0.1/api/sckami.php?time={days}&s={count}"
    response = requests.get(url)
    data = response.json()
    if data.get("code") == 200:
        return data.get("kamis", [])
    else:
        print("卡密生成失败:", data.get("message"))
        return []

def main():
    days = input("请输入卡密天数: ")
    count = input("请输入生成数量: ")
    try:
        days = int(days)
        count = int(count)
    except ValueError:
        print("请输入有效的数字")
        return

    kamis = generate_kami(days, count)
    if kamis:
        kami_str = "\n".join(kamis)
        with open("kami.txt", "w", encoding="utf-8") as f:
            f.write(kami_str)
        print("卡密已保存到 kami.txt")
    else:
        print("未生成卡密")

if __name__ == "__main__":
    main()