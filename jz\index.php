<?php
require 'vendor/autoload.php';

use <PERSON><PERSON><PERSON>\Phone\PhoneLocation;
use Jxlwqq\IdValidator\IdValidator;

require 'address.php';


require 'names.php';
use <PERSON><PERSON><PERSON>\NameGenerator;

// 允许跨域访问
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json; charset=utf-8');

// 获取手机号参数
$phone = $_GET['phone'] ?? '';

// 验证手机号格式
if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
    echo json_encode([
        'code' => 400,
        'message' => '手机号格式不正确',
        'data' => null
    ]);
    exit;
}

try {
    // 查询手机号归属地
    $phoneLocation = new PhoneLocation();
    $location = $phoneLocation->find($phone);
    
    // 初始化身份证验证器
    $idValidator = new IdValidator();
    
    // 随机生成性别（0: 女, 1: 男）
    $gender = rand(0, 1);
    
    // 生成符合归属地的身份证
    $fakeId = $idValidator->fakeId(true, $location['city']."市", $gender);
    
    // 生成随机姓名
    $name = NameGenerator::generate('rand');
    
    echo json_encode([
        'code' => 200,
        'message' => 'success',
        'data' => [
            'phone' => $phone,
            'location' => $location,
            'idcard' => $fakeId,
            'name' => $name,
            'address' => generateAddress($fakeId)
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => $e->getMessage(),
        'data' => null
    ]);
}