<?php
// 数据库连接参数
$host = 'localhost';
$port = '3306';
$user = 'api269sjk135';
$pwd = 'api269sjk135';
$dbname = 'api269sjk135';
$charset = 'utf8';

// 创建数据库连接
$mysqli = new mysqli($host, $user, $pwd, $dbname, $port);

// 检查连接是否成功
if ($mysqli->connect_error) {
    die("连接失败: " . $mysqli->connect_error);
}

// 设置字符集
$mysqli->set_charset($charset);

// 查询 "分享" 表中的所有数据，并按下载次数降序排序
$query = "SELECT * FROM 分享 ORDER BY 下载次数 DESC";
$result = $mysqli->query($query);

// 检查查询是否成功
if ($result && $result->num_rows > 0) {
    // 遍历数据并输出
    while ($row = $result->fetch_assoc()) {
        $fileId = $row['文件id'];
        $fileName = htmlspecialchars($row['文件名称']); // 防止 XSS 攻击
        $fileSize = htmlspecialchars($row['文件大小']);
        $fileDesc = mb_substr(htmlspecialchars($row['文件介绍']), 0, 50, 'utf-8'); // 文件介绍摘要
        $downloadCount = htmlspecialchars($row['下载次数']);
        $fileType = $row['分享类型'];
        $downloadUrl = htmlspecialchars($row['下载url']);
        $shareTime = $row['分享时间'];
        
        // 根据分享类型选择图标
        $icon = ($fileType == 'py脚本') ? './img/py.png' : './img/app.png';

        echo '
        <div class="share-item">
            <div class="share-left">
                <img src="' . $icon . '" alt="图标" class="file-icon">
            </div>
            <div class="share-right">
                <div class="file-info">
                    <strong>' . $fileName . '</strong>
                </div>
                <div class="file-description">
                    ' . $fileDesc . '...
                </div>
                <div class="file-footer">
                    <span>' . $fileSize . '</span>
                    <span>' . $downloadCount . ' 次下载</span>
                </div>
                <a href="./py/demo.php?id=' . $fileId . '" class="view-details">查看详情</a>
            </div>
        </div>
        ';
    }
} else {
    echo '暂无分享资源。';
}

// 关闭数据库连接
$mysqli->close();
?>

<!-- 引入 CSS 样式 -->
<link rel="stylesheet" href="style.css">
