<?php
session_start();

// 检查客户端 sessionStorage 是否有登录状态和 token
if (isset($_SESSION['loginStatus']) && $_SESSION['loginStatus'] === 1 && isset($_SESSION['token'])) {
    // 如果已经登录，继续执行
    $token = $_SESSION['token'];  // 获取全局变量中的 token
} else {
    // 如果登录状态无效或没有 token，则跳转到 login.php
    header("Location: login.php");
    exit();
}

// 接口 URL
$api_url = "https://appdmin.cpolar.top/api/demo.php?token=$token";

// 获取接口数据
$response = file_get_contents($api_url);
$data = json_decode($response, true);

// 检查接口返回的数据是否有效
if ($data['code'] !== 200) {
    // 如果接口返回错误，显示错误信息
    echo "<p>错误：{$data['message']}</p>";
    exit();
}

// 获取接口返回的用户和软件信息
$user = $data['user'];
$msg = $data['msg'];

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户主页</title>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome/css/font-awesome.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: rgba(0, 0, 0, 0.05);
            backdrop-filter: blur(10px);
        }
        .container {
            width: 100%;
            max-width: 800px;
            background-color: rgba(255, 255, 255, 0.85);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(8px);
        }
        h2 {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
        }
        .info-item .label {
            font-weight: bold;
            color: #333;
        }
        .info-item .value {
            color: #555;
        }
        .btn {
            display: block;
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 20px;
            transition: background-color 0.3s ease;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .msg-box {
            background: #fafafa;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #ddd;
            margin-top: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>

<div class="container">
    <h2>欢迎回来, 用户</h2>
    
    <!-- 用户信息展示 -->
    <div class="info-item">
        <div class="label">Token</div>
        <div class="value"><?php echo htmlspecialchars($user['token']); ?></div>
    </div>
    <div class="info-item">
        <div class="label">VIP 码</div>
        <div class="value"><?php echo htmlspecialchars($user['vipcode']); ?></div>
    </div>
    <div class="info-item">
        <div class="label">VIP 到期时间</div>
        <div class="value"><?php echo $user['viptime'] ? htmlspecialchars($user['viptime']) : '暂无VIP'; ?></div>
    </div>
    <div class="info-item">
        <div class="label">当前时间</div>
        <div class="value"><?php echo htmlspecialchars($user['time']); ?></div>
    </div>

    <!-- 软件信息展示 -->
    <div class="msg-box">
        <h4>软件信息</h4>
        <p><strong>版本号：</strong><?php echo htmlspecialchars($msg['appvid']); ?></p>
        <p><strong>软件状态：</strong><?php echo $msg['appcode'] == 1 ? '运营中' : '停运'; ?></p>
        <p><strong>公告：</strong><br><?php echo nl2br(htmlspecialchars($msg['appgg'])); ?></p>
        <p><strong>下载链接：</strong><a href="<?php echo htmlspecialchars($msg['appurl']); ?>" target="_blank">点击下载</a></p>
        <p><strong>背景图：</strong><img src="<?php echo htmlspecialchars($msg['appimg']); ?>" alt="背景图" style="max-width: 100%; border-radius: 8px;"></p>
        <p><strong>代理申请：</strong><a href="<?php echo htmlspecialchars($msg['dailiurl']); ?>" target="_blank">申请链接</a></p>
        <p><strong>官方QQ：</strong><?php echo htmlspecialchars($msg['qqqid']); ?></p>
    </div>

    <!-- 退出按钮 -->
    <button class="btn" onclick="logout()">退出登录</button>
</div>

<script>
    function logout() {
        if (confirm('确定要退出登录吗？')) {
            // 清除 session 中的登录状态
            window.location.href = 'logout.php'; // 跳转到退出登录的处理页
        }
    }
</script>

</body>
</html>
