<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>信息查询系统</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        :root {
            --glass-bg: rgba(255, 255, 255, 0.7);
            --glass-border: rgba(255, 255, 255, 0.3);
            --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: var(--glass-shadow);
            background: var(--glass-bg);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border: 1px solid var(--glass-border);
            overflow: hidden;
        }
        
        .card-header {
            background: linear-gradient(135deg, rgba(52, 58, 64, 0.9) 0%, rgba(73, 80, 87, 0.9) 100%);
            color: white;
            border-radius: 20px 20px 0 0 !important;
            padding: 1.5rem;
            border-bottom: 1px solid var(--glass-border);
        }
        
        .form-control {
            border-radius: 12px;
            padding: 0.75rem 1rem;
            border: 1px solid var(--glass-border);
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.25);
            border-color: #343a40;
            background: rgba(255, 255, 255, 1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #343a40 0%, #495057 100%);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        
        .result-box {
            margin-top: 20px;
            padding: 20px;
            border-radius: 15px;
            background: rgba(248, 249, 250, 0.9);
            border: 1px solid var(--glass-border);
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 500px;
            overflow-y: auto;
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading i {
            font-size: 2rem;
            color: #343a40;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .form-check-input:checked {
            background-color: #343a40;
            border-color: #343a40;
        }
        
        .token-info {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }
        
        .notice {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid var(--glass-border);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }
        
        .notice h4 {
            color: #dc3545;
            margin-bottom: 15px;
        }
        
        .notice p {
            margin-bottom: 10px;
            color: #495057;
        }
        
        .notice a {
            color: #0d6efd;
            text-decoration: none;
        }
        
        .notice a:hover {
            text-decoration: underline;
        }
        
        .animate__animated {
            animation-duration: 0.5s;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="notice animate__animated animate__fadeIn">
            <h4><i class="fas fa-exclamation-triangle me-2"></i>重要公告</h4>
            <p>本程序由泪绪团队制作 ⚠️</p>
            <p>- 作者：泪绪@qingxu6nb</p>
            <p>- 官方唯一授权售卖渠道：泪绪 <a href="https://t.me/qingxu6nb666" target="_blank">https://t.me/qingxu6nb666</a></p>
            <p class="text-danger"><i class="fas fa-exclamation-circle me-2"></i>❗️ 严禁倒卖，泛滥，镜像，一旦发现立即封卡 ❗️</p>
        </div>
        
        <div class="card animate__animated animate__fadeInUp">
            <div class="card-header text-center">
                <h2 class="mb-0"><i class="fas fa-search me-2"></i>信息查询系统</h2>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <label for="token" class="form-label">Token</label>
                    <input type="text" class="form-control" id="token" placeholder="请输入您的Token">
                    <div class="token-info">
                        <i class="fas fa-info-circle me-1"></i>Token用于验证您的身份和查询权限
                    </div>
                </div>
                <div class="mb-4">
                    <label for="query" class="form-label">查询内容</label>
                    <input type="text" class="form-control" id="query" placeholder="请输入中文姓名">
                    <div class="token-info">
                        <i class="fas fa-info-circle me-1"></i>请输入要查询的中文姓名
                    </div>
                </div>
                <div class="mb-4 form-check">
                    <input type="checkbox" class="form-check-input" id="onlyIdcard">
                    <label class="form-check-label" for="onlyIdcard">仅显示身份证号</label>
                </div>
                <button class="btn btn-primary w-100" onclick="search()">
                    <i class="fas fa-search me-2"></i>开始查询
                </button>
                <div id="loading" class="loading">
                    <i class="fas fa-spinner"></i>
                    <p class="mt-2">正在查询中...</p>
                </div>
                <div id="result" class="result-box" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        async function search() {
            const token = document.getElementById('token').value;
            const query = document.getElementById('query').value;
            const onlyIdcard = document.getElementById('onlyIdcard').checked;
            
            if (!token || !query) {
                alert('请填写完整信息');
                return;
            }

            const resultDiv = document.getElementById('result');
            const loadingDiv = document.getElementById('loading');
            
            resultDiv.style.display = 'none';
            loadingDiv.style.display = 'block';

            try {
                const url = `lm.php?msg=${encodeURIComponent(query)}&token=${encodeURIComponent(token)}${onlyIdcard ? '&only_idcard=1' : ''}`;
                const response = await fetch(url);
                const data = await response.json();

                loadingDiv.style.display = 'none';
                resultDiv.style.display = 'block';
                resultDiv.classList.add('animate__animated', 'animate__fadeIn');

                if (data.code === 200) {
                    if (onlyIdcard && data.idcards) {
                        resultDiv.innerHTML = data.idcards;
                    } else {
                        resultDiv.innerHTML = data.shuju;
                    }
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger">错误：${data.message}</div>`;
                }
            } catch (error) {
                loadingDiv.style.display = 'none';
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = '<div class="alert alert-danger">查询出错，请稍后重试</div>';
            }
        }

        // 添加回车键触发查询
        document.getElementById('query').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                search();
            }
        });
    </script>
</body>
</html>

