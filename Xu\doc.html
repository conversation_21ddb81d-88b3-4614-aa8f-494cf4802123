<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 文档 - 信息查询系统</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/highlight.js/11.7.0/styles/github.min.css" rel="stylesheet">
    <style>
        :root {
            --glass-bg: rgba(255, 255, 255, 0.7);
            --glass-border: rgba(255, 255, 255, 0.3);
            --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: var(--glass-shadow);
            background: var(--glass-bg);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border: 1px solid var(--glass-border);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .card-header {
            background: linear-gradient(135deg, rgba(52, 58, 64, 0.9) 0%, rgba(73, 80, 87, 0.9) 100%);
            color: white;
            padding: 1.5rem;
            border-bottom: 1px solid var(--glass-border);
        }
        
        .notice {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid var(--glass-border);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }
        
        .notice h4 {
            color: #dc3545;
            margin-bottom: 15px;
        }
        
        .notice p {
            margin-bottom: 10px;
            color: #495057;
        }
        
        .notice a {
            color: #0d6efd;
            text-decoration: none;
        }
        
        .notice a:hover {
            text-decoration: underline;
        }
        
        pre {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }
        
        code {
            font-family: 'Fira Code', monospace;
        }
        
        .endpoint {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .endpoint h3 {
            color: #343a40;
            margin-bottom: 15px;
        }
        
        .endpoint .method {
            font-weight: bold;
            color: #0d6efd;
        }
        
        .endpoint .url {
            font-family: monospace;
            background: rgba(0, 0, 0, 0.05);
            padding: 5px 10px;
            border-radius: 5px;
        }
        
        .param-table {
            width: 100%;
            margin: 15px 0;
        }
        
        .param-table th {
            background: rgba(52, 58, 64, 0.9);
            color: white;
            padding: 10px;
        }
        
        .param-table td {
            padding: 10px;
            border-bottom: 1px solid var(--glass-border);
        }
        
        .response-example {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="notice animate__animated animate__fadeIn">
            <h4><i class="fas fa-exclamation-triangle me-2"></i>重要公告</h4>
            <p>本程序由泪绪团队制作 ⚠️</p>
            <p>- 作者：泪绪@qingxu6nb</p>
            <p>- 官方唯一授权售卖渠道：泪绪 <a href="https://t.me/qingxu6nb666" target="_blank">https://t.me/qingxu6nb666</a></p>
            <p class="text-danger"><i class="fas fa-exclamation-circle me-2"></i>❗️ 严禁倒卖，泛滥，镜像，一旦发现立即封卡 ❗️</p>
        </div>

        <div class="card animate__animated animate__fadeInUp">
            <div class="card-header">
                <h2 class="mb-0"><i class="fas fa-book me-2"></i>API 文档</h2>
            </div>
            <div class="card-body">
                <h3>基本信息</h3>
                <p>本文档详细说明了信息查询系统的 API 接口使用方法。所有接口均返回 JSON 格式的数据。</p>

                <div class="endpoint">
                    <h3>查询接口</h3>
                    <p><span class="method">GET</span> <span class="url">/lm.php</span></p>
                    
                    <h4>请求参数</h4>
                    <table class="param-table">
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>必填</th>
                            <th>说明</th>
                        </tr>
                        <tr>
                            <td>msg</td>
                            <td>string</td>
                            <td>是</td>
                            <td>要查询的中文姓名</td>
                        </tr>
                        <tr>
                            <td>token</td>
                            <td>string</td>
                            <td>是</td>
                            <td>用户的访问令牌</td>
                        </tr>
                        <tr>
                            <td>only_idcard</td>
                            <td>boolean</td>
                            <td>否</td>
                            <td>是否只返回身份证号，值为1时只返回身份证号</td>
                        </tr>
                    </table>

                    <h4>响应参数</h4>
                    <table class="param-table">
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>说明</th>
                        </tr>
                        <tr>
                            <td>code</td>
                            <td>integer</td>
                            <td>状态码：200成功，400参数错误，401Token无效，403次数用完</td>
                        </tr>
                        <tr>
                            <td>message</td>
                            <td>string</td>
                            <td>状态说明</td>
                        </tr>
                        <tr>
                            <td>queries_left</td>
                            <td>integer</td>
                            <td>剩余查询次数</td>
                        </tr>
                        <tr>
                            <td>shuju</td>
                            <td>string</td>
                            <td>查询结果（完整信息）</td>
                        </tr>
                        <tr>
                            <td>idcards</td>
                            <td>string</td>
                            <td>查询结果（仅身份证号）</td>
                        </tr>
                        <tr>
                            <td>notice</td>
                            <td>object</td>
                            <td>公告信息</td>
                        </tr>
                    </table>

                    <h4>请求示例</h4>
                    <pre><code>GET /lm.php?msg=张三&token=your_token_here&only_idcard=1</code></pre>

                    <h4>成功响应示例</h4>
                    <pre><code>{
    "code": 200,
    "message": "查询成功",
    "queries_left": 99,
    "shuju": "查询结果示例\n姓名: 张三\n身份证: 123456789012345678",
    "idcards": "123456789012345678",
    "notice": {
        "title": "重要公告",
        "content": [
            "本程序由泪绪团队制作 ⚠️",
            "作者：泪绪@qingxu6nb",
            "官方唯一授权售卖渠道：泪绪 https://t.me/qingxu6nb666",
            "❗️ 严禁倒卖，泛滥，镜像，一旦发现立即封卡 ❗️"
        ]
    }
}</code></pre>

                    <h4>错误响应示例</h4>
                    <pre><code>{
    "code": 401,
    "message": "无效的Token",
    "notice": {
        "title": "重要公告",
        "content": [
            "本程序由泪绪团队制作 ⚠️",
            "作者：泪绪@qingxu6nb",
            "官方唯一授权售卖渠道：泪绪 https://t.me/qingxu6nb666",
            "❗️ 严禁倒卖，泛滥，镜像，一旦发现立即封卡 ❗️"
        ]
    }
}</code></pre>
                </div>

                <h3>注意事项</h3>
                <ul>
                    <li>所有请求必须包含有效的 token</li>
                    <li>token 对应的用户必须有足够的查询次数</li>
                    <li>查询内容必须是中文姓名</li>
                    <li>建议在请求时添加错误处理机制</li>
                    <li>请遵守使用规范，严禁倒卖、泛滥、镜像</li>
                </ul>

                <h3>状态码说明</h3>
                <table class="param-table">
                    <tr>
                        <th>状态码</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>200</td>
                        <td>查询成功</td>
                    </tr>
                    <tr>
                        <td>400</td>
                        <td>参数错误（如未提供查询内容）</td>
                    </tr>
                    <tr>
                        <td>401</td>
                        <td>Token无效或未提供</td>
                    </tr>
                    <tr>
                        <td>403</td>
                        <td>查询次数已用完</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', (event) => {
            document.querySelectorAll('pre code').forEach((el) => {
                hljs.highlightElement(el);
            });
        });
    </script>
</body>
</html> 