<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 引入数据库配置文件
require '../db.php';

// 获取动态参数 token
if (isset($_GET['token'])) {
    $token = $_GET['token'];

    // 检查 token 是否符合条件（仅包含字母和数字）
    if (!preg_match('/^[a-zA-Z0-9-]+$/', $token)) {
        echo json_encode(["code" => 400, "message" => "请输入正确的用户名", "user" => null], JSON_UNESCAPED_UNICODE);
        exit();
    }

    // 检查数据库中是否存在相同的 token
    $stmt = $mysqli->prepare("SELECT * FROM users WHERE token = ?");
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // 用户名已存在
        echo json_encode(["code" => 409, "message" => "用户名已存在", "user" => null], JSON_UNESCAPED_UNICODE);
    } else {
        // 插入新的用户数据
        $time = date('Y-m-d H:i:s');  // 获取当前时间
        $vipcode = 0;  // 非会员
        $viptime = '';  // VIP到期时间（标准格式）
        $tokencode = 200;  // 正常状态

        // 插入新用户
        $stmt = $mysqli->prepare("INSERT INTO users (token, time, vipcode, viptime, tokencode) VALUES (?, ?, ?, ?, ?)");
        $stmt->bind_param("ssisi", $token, $time, $vipcode, $viptime, $tokencode);

        if ($stmt->execute()) {
            // 注册成功，返回信息
            $user = ["token" => $token, "time" => $time];
            echo json_encode(["code" => 200, "message" => "已完成", "user" => $user], JSON_UNESCAPED_UNICODE);
        } else {
            // 注册失败，返回错误信息
            echo json_encode(["code" => 500, "message" => "异常，请稍后重试", "user" => null], JSON_UNESCAPED_UNICODE);
        }
    }

    $stmt->close();
} else {
    // 参数缺失
    echo json_encode(["code" => 400, "message" => "请输入正确的用户名", "user" => null], JSON_UNESCAPED_UNICODE);
}

$mysqli->close();
?>
