<?php
// 引入数据库配置文件
require '../db.php';

// 获取动态参数 time 和 s
if (isset($_GET['time']) && isset($_GET['s'])) {
    $time = $_GET['time'];
    $s = $_GET['s'];

    // 检查 time 是否为纯数字
    if (!preg_match('/^[0-9]+$/', $time)) {
        echo json_encode(["code" => 400, "message" => "请输入正确的卡密时间（纯数字）"], JSON_UNESCAPED_UNICODE);
        exit();
    }

    // 检查 s 是否为纯数字
    if (!preg_match('/^[0-9]+$/', $s)) {
        echo json_encode(["code" => 400, "message" => "请输入正确的生成数量（纯数字）"], JSON_UNESCAPED_UNICODE);
        exit();
    }

    $kamis = [];

    // 生成卡密并写入数据库
    for ($i = 0; $i < $s; $i++) {
        // 生成15位随机字母数字组合
        $kami = substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'), 0, 15);

        // 准备SQL语句
        $stmt = $mysqli->prepare("INSERT INTO kamis (kamitime, kami, oktoken, oktime, kamicode) VALUES (?, ?, NULL, NULL, '0')");

        // 检查SQL语句是否成功准备好
        if (!$stmt) {
            echo json_encode(["code" => 500, "message" => "SQL语句准备失败: " . $mysqli->error], JSON_UNESCAPED_UNICODE);
            exit();
        }

        // 绑定参数并执行
        $stmt->bind_param("ss", $time, $kami);
        
        if ($stmt->execute()) {
            // 保存生成的卡密
            $kamis[] = $kami;
        } else {
            echo json_encode(["code" => 500, "message" => "卡密生成失败，请稍后重试"], JSON_UNESCAPED_UNICODE);
            exit();
        }
    }

    // 返回生成的卡密
    echo json_encode(["code" => 200, "message" => "卡密生成成功", "kamis" => $kamis], JSON_UNESCAPED_UNICODE);

    $stmt->close();
} else {
    // 参数缺失
    echo json_encode(["code" => 400, "message" => "请输入正确的卡密时间和生成数量"], JSON_UNESCAPED_UNICODE);
}

$mysqli->close();
?>
