<?php
header('Content-Type: application/json');

$response = ['success' => false, 'message' => ''];

if (!isset($_POST['cardKey']) || empty($_POST['cardKey'])) {
    $response['message'] = '请输入卡密';
    echo json_encode($response);
    exit;
}

$cardKey = $_POST['cardKey'];
$cardsFile = __DIR__ . '/data/cards.json';

if (!file_exists($cardsFile)) {
    $response['message'] = '系统错误';
    echo json_encode($response);
    exit;
}

$cardsData = json_decode(file_get_contents($cardsFile), true);

if (!$cardsData || !isset($cardsData['cards'])) {
    $response['message'] = '系统错误';
    echo json_encode($response);
    exit;
}

$found = false;
foreach ($cardsData['cards'] as $card) {
    if ($card['key'] === $cardKey) {
        $found = true;
        
        if (!isset($card['expiry_date'])) {
            $response['message'] = '卡密数据错误';
            break;
        }
        
        $expiryDate = strtotime($card['expiry_date']);
        $now = time();
        
        if ($expiryDate < $now) {
            $response['message'] = '卡密已过期';
            break;
        }
        
        $response['success'] = true;
        session_start();
        $_SESSION['authenticated'] = true;
        $_SESSION['card_key'] = $cardKey;
        $_SESSION['expiry_date'] = $card['expiry_date'];
        break;
    }
}

if (!$found) {
    $response['message'] = '无效的卡密';
}

echo json_encode($response);