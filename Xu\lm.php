<?php

include 'db.php';
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 读取用户数据
$users = json_decode(file_get_contents('user.json'), true)['users'];

// 获取请求参数
$msg = $_GET['msg'] ?? '';
$token = $_GET['token'] ?? '';
$only_idcard = isset($_GET['only_idcard']) && $_GET['only_idcard'] == '1';

// 验证token
$validToken = false;
$userFound = false;

foreach ($users as $username => $user) {
    if ($user['token'] === $token) {
        $validToken = true;
        $userFound = true;
        if ($user['queries_left'] <= 0) {
            die(json_encode([
                'code' => 403,
                'message' => '查询次数已用完',
                'queries_left' => 0,
                'notice' => [
                    'title' => '重要公告',
                    'content' => [
                        '本程序由泪绪团队制作 ⚠️',
                        '作者：泪绪@qingxu6nb',
                        '官方唯一授权售卖渠道：泪绪 https://t.me/qingxu6nb666',
                        '❗️ 严禁倒卖，泛滥，镜像，一旦发现立即封卡 ❗️'
                    ]
                ]
            ], JSON_UNESCAPED_UNICODE));
        }
        break;
    }
}

if (!$validToken) {
    die(json_encode([
        'code' => 401,
        'message' => '无效的Token',
        'notice' => [
            'title' => '重要公告',
            'content' => [
                '本程序由泪绪团队制作 ⚠️',
                '作者：泪绪@qingxu6nb',
                '官方唯一授权售卖渠道：泪绪 https://t.me/qingxu6nb666',
                '❗️ 严禁倒卖，泛滥，镜像，一旦发现立即封卡 ❗️'
            ]
        ]
    ], JSON_UNESCAPED_UNICODE));
}

// 验证查询内容
if (empty($msg)) {
    die(json_encode([
        'code' => 400,
        'message' => '请输入查询内容',
        'queries_left' => $users[$username]['queries_left'],
        'notice' => [
            'title' => '重要公告',
            'content' => [
                '本程序由泪绪团队制作 ⚠️',
                '作者：泪绪@qingxu6nb',
                '官方唯一授权售卖渠道：泪绪 https://t.me/qingxu6nb666',
                '❗️ 严禁倒卖，泛滥，镜像，一旦发现立即封卡 ❗️'
            ]
        ]
    ], JSON_UNESCAPED_UNICODE));
}

// 计时器开始
$startTime = microtime(true);

// 验证是否为中文姓名
if (!preg_match('/^[\x{4e00}-\x{9fa5}]{2,}$/u', $msg)) {
    echo json_encode([
        'code' => 400,
        'message' => '请输入有效的中文姓名。'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$searchConn = new mysqli("localhost", "sgksjk", "sgksjk", "sgksjk");
if ($searchConn->connect_error) {
    echo json_encode([
        'code' => 500,
        'message' => '查询数据库连接失败: ' . $searchConn->connect_error
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 定义表和字段映射
$tables = [
    "48plc" => ["idcard" => "身份证", "phone" => "手机号", "name" => "名字"],
    "chphone" => ["idcard" => "身份证", "phone" => "电话", "name" => "姓名"],
    "随申码" => ["idcard" => "身份证", "phone" => "手机号", "name" => "姓名"],
    "学习通" => ["idcard" => "身份证", "phone" => "手机号", "name" => "姓名"],
    "上海10E" => ["idcard" => "身份证", "name" => "姓名"],
    "浙江学籍" => ["idcard" => "cardno", "phone" => "mobile", "name" => "username"],
    "aurora独家数据" => ["idcard" => "身份证", "phone" => "手机号", "name" => "姓名"],
    "银联数据" => ["name" => "username", "idcard" => "cardno"]
];

// 查询结果存储
$found = false;
$messages = [];
$idcards = [];
$uniqueRows = [];

foreach ($tables as $table => $indices) {
    if (isset($indices['name'])) {
        $queryField = $indices['name'];
        $idcardField = $indices['idcard'] ?? null;

        if (!$idcardField) continue;

        // 获取表的所有列
        $sql = "SHOW COLUMNS FROM `$table`";
        $columnsResult = $searchConn->query($sql);
        $columns = [];
        while ($col = $columnsResult->fetch_assoc()) {
            $columns[] = $col['Field'];
        }

        // 构建查询所有列的SQL
        $sql = "SELECT * FROM `$table` WHERE `$queryField` = ?";
        $stmt = $searchConn->prepare($sql);
        
        if ($stmt) {
            $stmt->bind_param('s', $msg);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $found = true;
                while ($data = $result->fetch_assoc()) {
                    // 构建包含所有列的字符串
                    $rowData = [];
                    foreach ($columns as $column) {
                        $rowData[] = "$column: {$data[$column]}";
                    }
                    $messages[] = "数据来源: $table\n" . implode(" | ", $rowData);
                    
                    // 收集身份证号
                    if (isset($data[$idcardField])) {
                        $idcards[] = $data[$idcardField];
                    }
                }
            }
            $stmt->close();
        }
    }
}

// 如果找到记录，减少查询次数
if ($found && isset($users[$username])) {
    $users[$username]['queries_left']--;
    file_put_contents('user.json', json_encode(['users' => $users], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
}

// 返回查询结果
$executionTime = round(microtime(true) - $startTime, 4);
$response = [
    'code' => $found ? 200 : 404,
    'message' => $found ? '查询成功' : '库中无记录。',
    'shuju' => $found ? implode("\n\n", $messages) : '',
    'execution_time' => $executionTime . ' 秒'
];

if ($only_idcard && $found) {
    $response['idcards'] = implode("\n", array_unique($idcards));
}

$response['notice'] = [
    'title' => '重要公告',
    'content' => [
        '本程序由泪绪团队制作 ⚠️',
        '作者：泪绪@qingxu6nb',
        '官方唯一授权售卖渠道：泪绪 https://t.me/qingxu6nb666',
        '❗️ 严禁倒卖，泛滥，镜像，一旦发现立即封卡 ❗️'
    ]
];

echo json_encode($response, JSON_UNESCAPED_UNICODE);

$searchConn->close();
?>