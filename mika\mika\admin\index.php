<?php
session_start();

// 简单的管理员验证，实际使用时应该使用更安全的认证方式
if (!isset($_SESSION['admin']) || $_SESSION['admin'] !== true) {
    if (isset($_POST['password'])) {
        if ($_POST['password'] === 'admin123') { // 实际使用时应该使用更安全的密码存储方式
            $_SESSION['admin'] = true;
        } else {
            $error = '密码错误';
        }
    }
    
    if (!isset($_SESSION['admin']) || $_SESSION['admin'] !== true) {
        ?>
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>管理员登录</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .login-form { max-width: 300px; margin: 0 auto; }
                input[type="password"] { width: 100%; padding: 8px; margin: 10px 0; }
                button { width: 100%; padding: 10px; background: #1890ff; color: white; border: none; cursor: pointer; }
                .error { color: red; margin-top: 10px; }
            </style>
        </head>
        <body>
            <div class="login-form">
                <h2>管理员登录</h2>
                <form method="post">
                    <input type="password" name="password" placeholder="请输入管理密码" required>
                    <button type="submit">登录</button>
                    <?php if (isset($error)) echo "<p class='error'>$error</p>"; ?>
                </form>
            </div>
        </body>
        </html>
        <?php
        exit;
    }
}

$cardsFile = __DIR__ . '/../data/cards.json';
$cardsData = json_decode(file_get_contents($cardsFile), true) ?: ['cards' => [], 'version' => '1.0'];

// 处理卡密操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create':
                $newCard = [
                    'key' => generateCardKey(),
                    'expiry_date' => date('Y-m-d', strtotime('+' . $_POST['validity'] . ' days')),
                    'created_at' => date('Y-m-d H:i:s')
                ];
                $cardsData['cards'][] = $newCard;
                file_put_contents($cardsFile, json_encode($cardsData, JSON_PRETTY_PRINT));
                $message = '卡密创建成功';
                break;
                
            case 'delete':
                if (isset($_POST['key'])) {
                    foreach ($cardsData['cards'] as $i => $card) {
                        if ($card['key'] === $_POST['key']) {
                            array_splice($cardsData['cards'], $i, 1);
                            file_put_contents($cardsFile, json_encode($cardsData, JSON_PRETTY_PRINT));
                            $message = '卡密删除成功';
                            break;
                        }
                    }
                }
                break;
        }
    }
}

// 生成随机卡密
function generateCardKey($length = 16) {
    $chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $key = '';
    for ($i = 0; $i < $length; $i++) {
        $key .= $chars[rand(0, strlen($chars) - 1)];
    }
    return $key;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密管理系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f2f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .create-form {
            margin-bottom: 30px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #fafafa;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #1890ff;
            color: white;
        }
        .btn-danger {
            background-color: #ff4d4f;
            color: white;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>卡密管理系统</h1>
            <form method="post" action="logout.php">
                <button type="submit" class="btn btn-danger">退出登录</button>
            </form>
        </div>
        
        <?php if (isset($message)): ?>
        <div class="message success"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>

        <div class="create-form">
            <h2>创建新卡密</h2>
            <form method="post">
                <input type="hidden" name="action" value="create">
                <label>
                    有效期（天数）：
                    <input type="number" name="validity" value="30" min="1" required>
                </label>
                <button type="submit" class="btn btn-primary">生成卡密</button>
            </form>
        </div>

        <h2>卡密列表</h2>
        <table>
            <thead>
                <tr>
                    <th>卡密</th>
                    <th>创建时间</th>
                    <th>过期时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($cardsData['cards'] as $card): ?>
                <tr>
                    <td><?php echo htmlspecialchars($card['key']); ?></td>
                    <td><?php echo htmlspecialchars($card['created_at']); ?></td>
                    <td><?php echo htmlspecialchars($card['expiry_date']); ?></td>
                    <td>
                        <form method="post" style="display: inline;">
                            <input type="hidden" name="action" value="delete">
                            <input type="hidden" name="key" value="<?php echo htmlspecialchars($card['key']); ?>">
                            <button type="submit" class="btn btn-danger">删除</button>
                        </form>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</body>
</html>