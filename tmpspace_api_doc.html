<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>VIP-临时空间 - iDatas社工API</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="/css/common.css">
  <link rel="stylesheet" href="/css/ios18.css">
</head>
<body>
  <!-- 公共顶栏 -->
  <div id="header-include"></div>
  <script>
    fetch('/components/header.html').then(r=>r.text()).then(html=>{
      document.getElementById('header-include').innerHTML = html;
    });
  </script>
  <header class="bg-primary text-white text-center py-5 mb-4 ios18-header">
    <div class="container">
      <h1 class="display-5 fw-bold">VIP-临时空间 <span class="badge bg-secondary ms-1">创建&查看</span></h1>
      <p class="lead">会员用户创建临时空间，生成访问链接。当有人访问链接时自动调用前置摄像头拍照并存储到本地。</p>
    </div>
  </header>
  <main class="container mb-5">
    <!-- 创建临时空间功能 -->
    <section class="mb-5">
      <h2 class="h4 mb-3">功能一：创建临时空间</h2>
      <div class="ios18-card card p-4 mb-4">
        <div class="mb-2"><strong>接口地址：</strong><span class="text-monospace">https://api.qnm6.top/api/zyj</span></div>
        <div class="mb-2"><strong>请求方法：</strong>GET</div>
        <div class="mb-2"><strong>返回格式：</strong>JSON</div>
      </div>
      <h3 class="h5 mt-4 mb-2">请求参数说明</h3>
      <div class="ios18-card card p-4 mb-4">
        <div class="table-responsive">
          <table class="table table-bordered align-middle text-center mb-0">
            <thead class="table-light">
              <tr><th>名称</th><th>必填</th><th>类型</th><th>说明</th></tr>
            </thead>
            <tbody>
              <tr><td>token</td><td>是</td><td>string</td><td>会员Token</td></tr>
              <tr><td>action</td><td>是</td><td>string</td><td>操作类型，固定值"create"</td></tr>
            </tbody>
          </table>
        </div>
        <div class="text-start text-secondary small mt-2">备注：只有有效的会员Token才能创建临时空间。</div>
      </div>
      <div class="mb-4">
        <strong>调用示例：</strong>
        <div class="ios18-card card p-3 mt-2 bg-light position-relative">
          <code id="create-demo">zyj?token=你的Token&amp;action=create</code>
          <button class="btn btn-sm btn-outline-primary ios18-copy-btn position-absolute top-0 end-0 m-2" data-copy-target="create-demo">拷贝</button>
        </div>
      </div>
      <h3 class="h5 mt-4 mb-2">返回示例</h3>
      <div class="ios18-card card p-3 bg-light mb-3 position-relative">
        <pre id="create-resp" class="mb-0" style="white-space:pre-wrap;">{
  "code": 200,
  "message": "临时空间创建成功",
  "tpurl": "https://yourdomain.com/tmpview/abc123def456",
  "pw": "a1b2c3d4"
}</pre>
        <button class="btn btn-sm btn-outline-primary ios18-copy-btn position-absolute top-0 end-0 m-2" data-copy-target="create-resp">拷贝</button>
      </div>
      <h3 class="h5 mt-4 mb-2">返回参数说明</h3>
      <div class="ios18-card card p-4 mb-4">
        <div class="table-responsive">
          <table class="table table-bordered align-middle text-center mb-0">
            <thead class="table-light">
              <tr><th>名称</th><th>类型</th><th>说明</th></tr>
            </thead>
            <tbody>
              <tr><td>code</td><td>int</td><td>状态码，200表示成功</td></tr>
              <tr><td>message</td><td>string</td><td>状态信息</td></tr>
              <tr><td>tpurl</td><td>string</td><td>临时空间访问链接（无动态参数）</td></tr>
              <tr><td>pw</td><td>string</td><td>查看密码</td></tr>
            </tbody>
          </table>
        </div>
      </div>
      <h3 class="h5 mt-4 mb-2">代码示例</h3>
      <div class="ios18-card card p-3 bg-light position-relative">
        <pre id="create-code" class="mb-0" style="white-space:pre-wrap;">fetch('https://api.qnm6.top/api/zyj?token=xxxxxxx&action=create')
  .then(res => res.json())
  .then(data => {
    console.log('访问链接:', data.tpurl);
    console.log('查看密码:', data.pw);
  });
</pre>
        <button class="btn btn-sm btn-outline-primary ios18-copy-btn position-absolute top-0 end-0 m-2" data-copy-target="create-code">拷贝</button>
      </div>
    </section>

    <!-- 查看图片功能 -->
    <section class="mb-5">
      <h2 class="h4 mb-3">功能二：查看拍摄图片</h2>
      <div class="ios18-card card p-4 mb-4">
        <div class="mb-2"><strong>接口地址：</strong><span class="text-monospace">https://api.qnm6.top/api/zyj</span></div>
        <div class="mb-2"><strong>请求方法：</strong>GET</div>
        <div class="mb-2"><strong>返回格式：</strong>JSON</div>
      </div>
      <h3 class="h5 mt-4 mb-2">请求参数说明</h3>
      <div class="ios18-card card p-4 mb-4">
        <div class="table-responsive">
          <table class="table table-bordered align-middle text-center mb-0">
            <thead class="table-light">
              <tr><th>名称</th><th>必填</th><th>类型</th><th>说明</th></tr>
            </thead>
            <tbody>
              <tr><td>token</td><td>是</td><td>string</td><td>会员Token</td></tr>
              <tr><td>pw</td><td>是</td><td>string</td><td>查看密码</td></tr>
              <tr><td>action</td><td>是</td><td>string</td><td>操作类型，固定值"view"</td></tr>
            </tbody>
          </table>
        </div>
        <div class="text-start text-secondary small mt-2">备注：需要提供正确的Token和密码才能查看图片。</div>
      </div>
      <div class="mb-4">
        <strong>调用示例：</strong>
        <div class="ios18-card card p-3 mt-2 bg-light position-relative">
          <code id="view-demo">zyj?token=你的Token&amp;pw=查看密码&amp;action=view</code>
          <button class="btn btn-sm btn-outline-primary ios18-copy-btn position-absolute top-0 end-0 m-2" data-copy-target="view-demo">拷贝</button>
        </div>
      </div>
      <h3 class="h5 mt-4 mb-2">返回示例</h3>
      <div class="ios18-card card p-3 bg-light mb-3 position-relative">
        <pre id="view-resp" class="mb-0" style="white-space:pre-wrap;">{
  "code": 200,
  "message": "获取成功",
  "imgurl": [
    "https://yourdomain.com/tpimg/abc123_1234567890_def456.jpg",
    "https://yourdomain.com/tpimg/abc123_1234567891_ghi789.jpg"
  ],
  "count": 2
}</pre>
        <button class="btn btn-sm btn-outline-primary ios18-copy-btn position-absolute top-0 end-0 m-2" data-copy-target="view-resp">拷贝</button>
      </div>
      <h3 class="h5 mt-4 mb-2">返回参数说明</h3>
      <div class="ios18-card card p-4 mb-4">
        <div class="table-responsive">
          <table class="table table-bordered align-middle text-center mb-0">
            <thead class="table-light">
              <tr><th>名称</th><th>类型</th><th>说明</th></tr>
            </thead>
            <tbody>
              <tr><td>code</td><td>int</td><td>状态码，200表示成功</td></tr>
              <tr><td>message</td><td>string</td><td>状态信息</td></tr>
              <tr><td>imgurl</td><td>array</td><td>图片URL数组</td></tr>
              <tr><td>count</td><td>int</td><td>图片数量/拍摄次数</td></tr>
            </tbody>
          </table>
        </div>
      </div>
      <h3 class="h5 mt-4 mb-2">代码示例</h3>
      <div class="ios18-card card p-3 bg-light position-relative">
        <pre id="view-code" class="mb-0" style="white-space:pre-wrap;">fetch('https://api.qnm6.top/api/zyj?token=xxxxxxx&pw=a1b2c3d4&action=view')
  .then(res => res.json())
  .then(data => {
    console.log('图片数量:', data.count);
    data.imgurl.forEach((url, index) => {
      console.log(`图片${index + 1}:`, url);
    });
  });
</pre>
        <button class="btn btn-sm btn-outline-primary ios18-copy-btn position-absolute top-0 end-0 m-2" data-copy-target="view-code">拷贝</button>
      </div>
    </section>

    <!-- 使用说明 -->
    <section class="mb-5">
      <h2 class="h4 mb-3">使用说明</h2>
      <div class="ios18-card card p-4 mb-4">
        <h3 class="h6 mb-3">工作流程：</h3>
        <ol class="mb-3">
          <li><strong>创建空间：</strong>使用Token调用创建接口，获得访问链接和查看密码</li>
          <li><strong>分享链接：</strong>将tpurl链接分享给目标用户</li>
          <li><strong>自动拍照：</strong>用户访问链接时自动调用前置摄像头拍照</li>
          <li><strong>查看结果：</strong>使用Token和密码查看拍摄的所有照片</li>
        </ol>

        <h3 class="h6 mb-3">重要特性：</h3>
        <ul class="mb-3">
          <li><strong>隐蔽性：</strong>访问链接不包含任何动态参数，如 www.yourdomain.com/tmpview/xxxxxxx</li>
          <li><strong>自动化：</strong>访问链接后自动调用前置摄像头，无需用户操作</li>
          <li><strong>简洁显示：</strong>拍照完成后只显示"OK!"，不暴露任何信息</li>
          <li><strong>本地存储：</strong>所有照片存储在服务器本地，安全可控</li>
          <li><strong>双重验证：</strong>查看图片需要Token和密码双重验证</li>
        </ul>

        <h3 class="h6 mb-3">注意事项：</h3>
        <div class="alert alert-warning">
          <ul class="mb-0">
            <li>前置摄像头功能需要HTTPS环境或localhost才能正常工作</li>
            <li>用户首次访问需要授权摄像头权限</li>
            <li>建议定期清理过期的临时空间数据</li>
            <li>请合法合规使用此功能，遵守相关法律法规</li>
          </ul>
        </div>
      </div>
    </section>
  </main>
  <footer class="text-center py-4 mt-5 border-top text-muted bg-light">
    <small>© 2023-2025 iDatas社工API;官方频道@idatas8</small>
  </footer>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  <script src="/js/common.js"></script>
</body>
</html>
