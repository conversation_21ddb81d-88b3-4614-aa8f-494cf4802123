<?php
// 快速测试修复后的功能
header('Content-Type: text/html; charset=utf-8');

echo "<h1>🔧 语法修复验证</h1>";

try {
    // 尝试包含修复后的文件
    include_once 'index.php';
    echo "<p style='color:green;'>✅ 文件加载成功，语法错误已修复！</p>";
    
    echo "<h2>系统状态</h2>";
    echo "<p><strong>专业包状态:</strong> " . ($useAdvancedLibs ? "✅ 已加载" : "❌ 未加载") . "</p>";
    
    echo "<h2>快速功能测试</h2>";
    
    // 测试身份证功能
    echo "<h3>身份证测试</h3>";
    $testId = "110101199001011234";
    $idInfo = extractIdCardInfo($testId);
    
    if (!empty($idInfo)) {
        echo "<p style='color:green;'>✅ 身份证功能正常</p>";
        echo "<ul>";
        foreach ($idInfo as $key => $value) {
            echo "<li><strong>$key:</strong> $value</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color:red;'>❌ 身份证功能异常</p>";
    }
    
    // 测试手机号功能
    echo "<h3>手机号测试</h3>";
    $testPhone = "13812345678";
    $phoneInfo = getPhoneLocation($testPhone);
    
    if (!empty($phoneInfo)) {
        echo "<p style='color:green;'>✅ 手机号功能正常</p>";
        echo "<ul>";
        foreach ($phoneInfo as $key => $value) {
            echo "<li><strong>$key:</strong> $value</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color:red;'>❌ 手机号功能异常</p>";
    }
    
    echo "<h2>修复说明</h2>";
    echo "<ul>";
    echo "<li>✅ 修复了 'unexpected token use' 语法错误</li>";
    echo "<li>✅ 移除了条件内的use语句</li>";
    echo "<li>✅ 使用完全限定类名代替use语句</li>";
    echo "<li>✅ 保持了专业包和简化版本的智能切换功能</li>";
    echo "</ul>";
    
    echo "<h2>API测试</h2>";
    echo "<p>现在可以正常调用API了：</p>";
    echo "<code>GET /extend/api/sgzh/index.php?msg=110101199001011234&token=your_token</code>";
    
} catch (ParseError $e) {
    echo "<p style='color:red;'>❌ 语法错误仍然存在: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color:orange;'>⚠️ 运行时错误: " . $e->getMessage() . "</p>";
}

?>

<style>
body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f9f9f9;
}
h1, h2, h3 {
    color: #333;
}
code {
    background: #f4f4f4;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: monospace;
    display: inline-block;
    margin: 5px 0;
}
ul {
    background: #fff;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
