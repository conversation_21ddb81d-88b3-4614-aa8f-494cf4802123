<?php

include 'db.php';
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');
// 获取动态参数
$token = isset($_GET['token']) ? trim($_GET['token']) : '';
$phone = isset($_GET['phone']) ? trim($_GET['phone']) : '';
$msg = isset($_GET['msg']) ? trim($_GET['msg']) : '';
// 验证 Token
if (empty($token)) {
    echo json_encode(['code' => 400, 'message' => '请提供有效的token。',
    '频道' => "@nfgzs",
    '官网' => '【及时更新,更多接口】https://api.qnm6.top/'
], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

// 检查用户权限
$stmt = $mysqli->prepare("SELECT vipcode, viptime FROM users WHERE token = ?");
$stmt->bind_param('s', $token);
$stmt->execute();
$stmt->bind_result($vipcode, $viptime);
$stmt->fetch();
$stmt->close();

if (!$vipcode || $vipcode == '0') {
    echo json_encode(['code' => 403, 'message' => '您不是会员，无法使用。',
    '频道' => "@nfgzs",
    '官网' => '【及时更新,更多接口】https://api.qnm6.top/'
], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

$currentDate = new DateTime();
$vipExpireDate = DateTime::createFromFormat('Y-m-d H:i:s', $viptime);
if (!$vipExpireDate || $vipExpireDate <= $currentDate) {
    echo json_encode(['code' => 403, 'message' => "VIP已过期，请续费后再试。",
    '频道' => "@nfgzs",
    '官网' => '【及时更新,更多接口】https://api.qnm6.top/'
], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

// 检查 VIP 到期时间是否大于 35 天
$interval = $currentDate->diff($vipExpireDate);
if ($interval->days <= 35) {
    echo json_encode([
        'code' => 403, 
        'message' => "高级功能会员到期时间必须大于35天，当前到期时间：" . $vipExpireDate->format('Y-m-d H:i:s'),
        '频道' => "@nfgzs",
        '官网' => '【及时更新,更多接口】https://api.qnm6.top/'
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

$statusStr = array(
            "00000" => "短信发送成功",
            "F0001" => "参数appkey未填写",
            "F0002" => "参数appcode未填写",
            "F0003" => "参数phone未填写",
            "F0004" => "参数sign未填写",
            "F0005" => "参数timestamp未填写",
            "F0006" => "appkey不存在",
            "F0007" => "账号已经关闭",
            "F0008" => "sign检验错误",
            "F0009" => "账号下没有业务",
			"F0010" => "业务不存在",
			"F0011" => "手机号码超过1000个",
			"F0012" => "timestamp不是数字",
			"F0013" => "timestamp过期超过5分钟",
			"F0014" => "请求ip不在白名单内",
			"F0015" => "余额不足",
			"F0016" => "手机号码无效",
			"F0017" => "没有可用的业务",
			"F0022" => "参数msg未填写",
			"F0023" => "msg超过了1000个字",
			"F0024" => "extend不是纯数字",
			"F0025" => "内容签名未报备/无签名",
			"F0039" => "参数sms未填写",
			"F0040" => "参数sms格式不正确",
			"F0041" => "短信条数超过1000条",
			"F0050" => "无数据",
			"F0100" => "未知错误",
);
$url = 'http://***********:9090/sms/batch/v1';
//拼接签名
$time = explode (" ", microtime () );   
$time = $time [1] . ($time [0] * 1000);   
$time2 = explode ( ".", $time );   
$time = $time2 [0];
$appkey = '2691354503';
$appcode = '5418';
$appsecret = 'S2095297';
$signcode = md5($appkey.$appsecret.$time);
//创建数组 写入参数
$arr = array(
	'appkey'=>$appkey,
	'appcode'=>$appcode,
	'sign'=>$signcode,
	'phone'=>$phone,
	'msg'=>'【KMHSGK】'. $msg,
	'timestamp'=>$time,
);
$data_string = json_encode($arr);

//POST
$ch = curl_init($url);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
curl_setopt($ch, CURLOPT_POSTFIELDS,$data_string);
curl_setopt($ch, CURLOPT_RETURNTRANSFER,true);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
	'Content-Type: application/json',
	'Content-Length: ' . strlen($data_string)
));

$json = curl_exec($ch);
echo($json."</br>");
//解析返回值
$json_Array = json_decode($json,true);
$result = $json_Array['code'];
//短信发送成功返回True，失败返回false
if ($result == '00000') {
    echo json_encode(array('code' => "200", 'message' => $statusStr[$result]), JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
} else {
    echo json_encode(array('code' => "500", 'message' =>  $statusStr[$result] . '. Code: ' . $result), JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}
curl_close($ch);
?>
