/* 基本页面样式 */
body {
    font-family: 'Poppins', sans-serif;
    background: #f9f9f9;
    margin: 0;
    padding: 0;
    color: #333;
}

/* 顶栏设计，iOS 17 风格 */
.topbar {
    background: linear-gradient(135deg, #007AFF, #5AC8FA);
    color: white;
    padding: 16px 24px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 16px;
    max-width: 1200px;
    margin: 20px auto;
}

.topbar h1 {
    font-size: 28px;
    font-weight: 600;
}

.topbar p {
    font-size: 14px;
    opacity: 0.8;
}

/* 分享项列表 */
.share-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); /* 自适应多列 */
    gap: 20px;
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

/* 分享项卡片 */
.share-item {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    text-align: left; /* 左对齐 */
}

.share-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

/* 文件图标（左侧） */
.file-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #ddd;
    margin-right: 20px; /* 保证文件图标与右侧文本有间距 */
}

.share-item .share-left {
    display: flex;
    align-items: center;
    margin-bottom: 20px; /* 让文件图标与右侧内容分开 */
}

/* 文件信息（右侧） */
.share-item .file-info {
    font-size: 18px;
    font-weight: 600;
    color: #007AFF;
    margin-bottom: 12px;
}

.share-item .file-description {
    font-size: 14px;
    color: #666;
    margin-bottom: 16px;
    line-height: 1.4;
}

.share-item .file-footer {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #888;
}

/* 查看详情按钮 */
.share-item .view-details {
    display: inline-block;
    margin-top: 12px;
    background: #007AFF;
    color: white;
    padding: 10px 20px;
    border-radius: 24px;
    font-size: 14px;
    text-decoration: none;
    transition: background-color 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    width: 100%; /* 让按钮占满剩余空间 */
    box-sizing: border-box; /* 确保按钮不溢出卡片边框 */
    max-width: 200px; /* 最大宽度控制，避免溢出 */
}

.share-item .view-details:hover {
    background-color: #0057D2;
}

/* 自适应设计：中等设备及以下 */
@media (max-width: 1024px) {
    .share-item {
        padding: 16px;
    }

    .file-info {
        font-size: 16px;
    }

    .file-description {
        font-size: 13px;
    }
}

/* 小屏设备：手机端优化 */
@media (max-width: 768px) {
    .topbar {
        padding: 12px 16px;
    }

    .share-item {
        padding: 12px;
    }

    .share-item .file-footer {
        flex-direction: column;
        align-items: center;
    }

    /* 按钮样式优化：避免溢出 */
    .share-item .view-details {
        max-width: 90%; /* 让按钮在小屏上占据更多宽度，但避免溢出 */
        margin-top: 10px;
    }
}

/* 底部导航栏 */
.bottom-bar {
    background: linear-gradient(135deg, #007AFF, #5AC8FA);
    color: white;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 10px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.1);
}

.bottom-bar-item {
    margin: 0 15px;
    text-align: center;
}

.bottom-bar-item a {
    color: white;
    font-size: 16px;
    text-decoration: none;
    display: inline-block;
    transition: color 0.3s ease;
}

.bottom-bar-item a:hover {
    color: #FF9500;
}

.bottom-bar-item img {
    width: 24px;
    height: 24px;
    margin-bottom: 8px;
}
