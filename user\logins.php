<?php
// 数据库连接
$servername = "127.0.0.1";
$username = "botdemo";
$password = "botdemo";
$dbname = "botdemo";

$conn = new mysqli($servername, $username, $password, $dbname);

// 检查连接
if ($conn->connect_error) {
    die("连接失败: " . $conn->connect_error);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 获取用户输入的验证码
    $captcha = $_POST['captcha'];

    // 查询验证码
    $sql = "SELECT * FROM `验证码逻辑` WHERE `验证码` = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $captcha);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // 验证码存在，检查状态
        $row = $result->fetch_assoc();
        
        if ($row['验证码状态'] == '1') {
            // 验证码已使用
            echo json_encode([
                'code' => 500,
                'message' => '验证码错误！'
            ]);
        } else {
            // 验证码未使用，更新验证码状态
            $ip = $_SERVER['REMOTE_ADDR'];
            $triggerId = $row['触发ID'];
            
            // 更新验证码状态为已使用，并记录IP
            $update_sql = "UPDATE `验证码逻辑` SET `验证码状态` = '1', `登陆IP` = ? WHERE `验证码` = ?";
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param("ss", $ip, $captcha);
            $update_stmt->execute();

            // 返回成功，返回触发ID作为token
            echo json_encode([
                'code' => 200,
                'message' => '验证码验证成功',
                'token' => $triggerId
            ]);
        }
    } else {
        // 验证码不存在
        echo json_encode([
            'code' => 404,
            'message' => '验证码错误！'
        ]);
    }

    $stmt->close();
}

$conn->close();
?>
