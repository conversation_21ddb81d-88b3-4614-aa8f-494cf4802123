<?php
/*
 * 充值页面
 * 用于用户充值会员时间
 */
?>
<!doctype html>
<html lang="zh-cn" class="">
<head>
    <meta charset="utf-8">
    <title>会员充值 - iDatas SGKAPI</title>
    <meta name="description" content="会员充值页面" />
    <meta name="keywords" content="充值,会员,VIP" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="/assets/img/favicons/favicon.png">
    <link rel="stylesheet" id="css-main" href="/assets/css/codebase.min-5.4.css">
    <link href="https://cdn.staticfile.org/nprogress/0.2.0/nprogress.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .recharge-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .recharge-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .recharge-card.selected {
            border-color: #3b82f6 !important;
            background-color: #eff6ff !important;
        }
        .loading {
            display: none;
        }
        .result-box {
            display: none;
            margin-top: 20px;
        }
    </style>
</head>
<body>
<div id="page-container" class="enable-page-overlay side-scroll page-header-fixed main-content-boxed remember-theme side-trans-enabled">
    <header id="page-header">
        <div class="content-header">
            <div class="space-x-1">
                <div class="content-header justify-content-lg-center">
                    <div>
                        <span class="smini-visible fw-bold tracking-wide fs-lg">
                            c<span class="text-primary">c</span>
                        </span>
                        <a class="link-fx fw-bold tracking-wide mx-auto" href="../">
                            <span class="smini-hidden">
                                <i class="fa fa-fire text-primary"></i>
                                <span class="fs-4 text-dual">iDatas</span>
                                <span class="fs-4 text-primary">SGKAPI</span>
                            </span>
                        </a>
                    </div>
                </div>
            </div>
            <div class="space-x-1">
                <a class="btn btn-sm btn-alt-secondary" href="./token.php" target="_blank">
                    <i class="fab fa-github-alt m-1"></i>注册Token</a>
                <a class="btn btn-sm btn-alt-secondary" href="./index.php">
                    <i class="fa fa-home m-1"></i>返回首页</a>
            </div>
        </div>
    </header>

    <main id="main-container">
        <div class="content">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6 col-xl-5">
                    <div class="block block-rounded">
                        <div class="block-header block-header-default text-center">
                            <h3 class="block-title">
                                <i class="fa fa-crown text-warning me-2"></i>
                                会员充值
                            </h3>
                        </div>
                        <div class="block-content">
                            <form id="rechargeForm">
                                <!-- Token输入 -->
                                <div class="mb-4">
                                    <label for="token" class="form-label">用户Token <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control form-control-lg" id="token" name="token" 
                                           placeholder="请输入您的Token" required>
                                    <div class="form-text">Token是您的唯一身份标识，请确保输入正确</div>
                                </div>

                                <!-- 充值时间选择 -->
                                <div class="mb-4">
                                    <label class="form-label">选择充值时间 <span class="text-danger">*</span></label>
                                    <div class="row g-3">
                                        <div class="col-6 col-md-4">
                                            <div class="recharge-card border rounded p-3 text-center" data-days="1">
                                                <div class="fs-5 fw-bold text-primary">1天</div>
                                                <div class="fs-sm text-muted">体验会员</div>
                                            </div>
                                        </div>
                                        <div class="col-6 col-md-4">
                                            <div class="recharge-card border rounded p-3 text-center" data-days="7">
                                                <div class="fs-5 fw-bold text-primary">1周</div>
                                                <div class="fs-sm text-muted">周卡会员</div>
                                            </div>
                                        </div>
                                        <div class="col-6 col-md-4">
                                            <div class="recharge-card border rounded p-3 text-center" data-days="30">
                                                <div class="fs-5 fw-bold text-primary">1月</div>
                                                <div class="fs-sm text-muted">月卡会员</div>
                                            </div>
                                        </div>
                                        <div class="col-6 col-md-4">
                                            <div class="recharge-card border rounded p-3 text-center" data-days="90">
                                                <div class="fs-5 fw-bold text-primary">1季</div>
                                                <div class="fs-sm text-muted">季卡会员</div>
                                            </div>
                                        </div>
                                        <div class="col-6 col-md-4">
                                            <div class="recharge-card border rounded p-3 text-center" data-days="365">
                                                <div class="fs-5 fw-bold text-primary">1年</div>
                                                <div class="fs-sm text-muted">年卡会员</div>
                                            </div>
                                        </div>
                                        <div class="col-6 col-md-4">
                                            <div class="recharge-card border rounded p-3 text-center" data-days="99999">
                                                <div class="fs-5 fw-bold text-warning">永久</div>
                                                <div class="fs-sm text-muted">永久会员</div>
                                            </div>
                                        </div>
                                    </div>
                                    <input type="hidden" id="selectedDays" name="days" value="">
                                </div>

                                <!-- 充值按钮 -->
                                <div class="mb-4">
                                    <button type="submit" class="btn btn-primary btn-lg w-100" id="rechargeBtn">
                                        <i class="fa fa-credit-card me-2"></i>
                                        确认充值
                                    </button>
                                </div>

                                <!-- 加载状态 -->
                                <div class="loading text-center" id="loading">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">充值中...</span>
                                    </div>
                                    <div class="mt-2 text-muted">正在处理充值请求，请稍候...</div>
                                </div>

                                <!-- 结果显示 -->
                                <div class="result-box" id="resultBox">
                                    <div class="alert" id="resultAlert">
                                        <div id="resultMessage"></div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 说明信息 -->
                    <div class="block block-rounded">
                        <div class="block-content">
                            <h5 class="mb-3">
                                <i class="fa fa-info-circle text-info me-2"></i>
                                充值说明
                            </h5>
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fa fa-check text-success me-2"></i>
                                    充值成功后，您的会员状态将立即生效
                                </li>
                                <li class="mb-2">
                                    <i class="fa fa-check text-success me-2"></i>
                                    如果当前已是会员，充值时间将在现有到期时间基础上延长
                                </li>
                                <li class="mb-2">
                                    <i class="fa fa-check text-success me-2"></i>
                                    永久会员将获得99999天的会员时长
                                </li>
                                <li class="mb-2">
                                    <i class="fa fa-check text-success me-2"></i>
                                    如有问题请联系客服：@idatas6
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer id="page-footer">
        <div class="content py-3">
            <div class="row fs-sm">
                <div class="col-sm-6 order-sm-1 py-1 text-center text-sm-start">
                    <a class="fw-semibold text-muted" href="javascript:void (0);">© 2024 iDatas SGKAPI</a>
                </div>
            </div>
        </div>
    </footer>
</div>

<script>
$(document).ready(function() {
    // 选择充值时间
    $('.recharge-card').click(function() {
        $('.recharge-card').removeClass('selected');
        $(this).addClass('selected');
        $('#selectedDays').val($(this).data('days'));
    });

    // 表单提交
    $('#rechargeForm').submit(function(e) {
        e.preventDefault();
        
        const token = $('#token').val().trim();
        const days = $('#selectedDays').val();
        
        // 验证输入
        if (!token) {
            showResult('请输入Token', 'danger');
            return;
        }
        
        if (!days) {
            showResult('请选择充值时间', 'danger');
            return;
        }
        
        // 显示加载状态
        $('#loading').show();
        $('#resultBox').hide();
        $('#rechargeBtn').prop('disabled', true);
        
        // 发送充值请求
        $.ajax({
            url: 'api/cz.php',
            type: 'GET',
            data: {
                token: token,
                time: days
            },
            dataType: 'json',
            success: function(response) {
                $('#loading').hide();
                $('#rechargeBtn').prop('disabled', false);
                
                if (response.code === 200) {
                    showResult(`充值成功！<br>Token: ${response.user.token}<br>VIP到期时间: ${response.user.viptime}`, 'success');
                    // 清空表单
                    $('#token').val('');
                    $('.recharge-card').removeClass('selected');
                    $('#selectedDays').val('');
                } else {
                    showResult(response.message || '充值失败，请检查Token是否正确', 'danger');
                }
            },
            error: function(xhr, status, error) {
                $('#loading').hide();
                $('#rechargeBtn').prop('disabled', false);
                showResult('网络错误，请稍后重试', 'danger');
            }
        });
    });
    
    // 显示结果
    function showResult(message, type) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        $('#resultAlert').removeClass('alert-success alert-danger').addClass(alertClass);
        $('#resultMessage').html(message);
        $('#resultBox').show();
        
        // 滚动到结果区域
        $('html, body').animate({
            scrollTop: $('#resultBox').offset().top - 100
        }, 500);
    }
});
</script>
</body>
</html> 