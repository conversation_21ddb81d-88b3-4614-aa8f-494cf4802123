<?php
// 引入数据库配置文件
require_once '../db.php';

// 开始事务
$mysqli->begin_transaction();

try {
    // 获取今日启动次数
    $tongjiQuery = "SELECT `今日启动次数` FROM tongji WHERE 1";
    $result = $mysqli->query($tongjiQuery);
    
    if ($result->num_rows === 0) {
        throw new Exception("数据获取失败");
    }

    $data = $result->fetch_assoc();
    $todayLaunchCount = $data['今日启动次数'];

    // 更新昨日启动次数
    $updateQuery = "UPDATE tongji SET `昨日启动次数` = `今日启动次数`, `今日启动次数` = 0 WHERE 1";
    if (!$mysqli->query($updateQuery)) {
        throw new Exception("更新失败: " . $mysqli->error);
    }

    // 提交事务
    $mysqli->commit();
    
    echo json_encode(["code" => 200, "message" => "数据更新成功"], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    // 回滚事务
    $mysqli->rollback();
    echo json_encode(["code" => 500, "message" => "操作失败: " . $e->getMessage()], JSON_UNESCAPED_UNICODE);
}

$mysqli->close();
?>
