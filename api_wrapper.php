<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0);

// 卡密文件路径
$cards_file = 'api_cards.json';

// 上游API配置
$upstream_api_url = '127.0.0.1/api/dujia/index.php';
$upstream_token = '7f18238856e47ef6'; // 请替换为实际的上游token

/**
 * 读取卡密数据
 */
function loadCards($file) {
    if (!file_exists($file)) {
        return [];
    }
    $content = file_get_contents($file);
    return json_decode($content, true) ?: [];
}

/**
 * 保存卡密数据
 */
function saveCards($file, $cards) {
    return file_put_contents($file, json_encode($cards, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}



/**
 * 调用上游API
 */
function callUpstreamAPI($token, $name, $idcard, $lx) {
    global $upstream_api_url, $upstream_token;

    $url = "http://{$upstream_api_url}?token={$upstream_token}&name=" . urlencode($name) . "&idcard=" . urlencode($idcard) . "&lx=" . urlencode($lx);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);

    if ($response === false || !empty($curl_error)) {
        return [
            'code' => 500,
            'message' => '上游API调用失败: ' . ($curl_error ?: 'Unknown error')
        ];
    }

    $data = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        return [
            'code' => 500,
            'message' => '上游API返回数据格式错误: ' . json_last_error_msg()
        ];
    }

    // 确保图片URL是完整的
    if (isset($data['data']['image_url']) && !empty($data['data']['image_url'])) {
        $image_url = $data['data']['image_url'];
        // 如果不是完整URL，添加协议
        if (!preg_match('/^https?:\/\//', $image_url)) {
            $data['data']['image_url'] = 'http://' . $image_url;
        }
    }

    return $data;
}

/**
 * 记录日志
 */
function logRequest($card_key, $name, $idcard, $lx, $result) {
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'card_key' => $card_key,
        'name' => $name,
        'idcard' => $idcard,
        'lx' => $lx,
        'result' => $result
    ];
    
    $log_file = 'api_logs/api_log_' . date('Y-m-d') . '.txt';
    $log_dir = dirname($log_file);
    
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    file_put_contents($log_file, json_encode($log_entry, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
}

// 主处理逻辑
try {
    // 检查请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('只支持GET请求', 405);
    }
    
    // 获取参数
    $card_key = $_GET['card_key'] ?? '';
    $name = $_GET['name'] ?? '';
    $idcard = $_GET['idcard'] ?? '';
    $lx = $_GET['lx'] ?? '';
    
    // 验证必需参数
    if (empty($card_key)) {
        throw new Exception('缺少card_key参数', 400);
    }
    
    if (empty($name)) {
        throw new Exception('缺少name参数', 400);
    }
    
    if (empty($idcard)) {
        throw new Exception('缺少idcard参数', 400);
    }
    
    if (empty($lx)) {
        throw new Exception('缺少lx参数', 400);
    }
    
    // 验证查询类型
    if (!in_array($lx, ['1', '2'])) {
        throw new Exception('lx参数必须为1或2', 400);
    }
    
    // 先验证卡密但不扣减次数
    $cards = loadCards($cards_file);
    $card_index = -1;
    $current_card = null;

    foreach ($cards as $index => $card) {
        if ($card['card_key'] === $card_key) {
            $card_index = $index;
            $current_card = $card;
            break;
        }
    }

    if ($card_index === -1) {
        $response = [
            'code' => 403,
            'message' => '卡密不存在'
        ];
        logRequest($card_key, $name, $idcard, $lx, $response);
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    if ($current_card['remaining_uses'] <= 0) {
        $response = [
            'code' => 403,
            'message' => '卡密使用次数已用完'
        ];
        logRequest($card_key, $name, $idcard, $lx, $response);
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 调用上游API
    $upstream_response = callUpstreamAPI($upstream_token, $name, $idcard, $lx);

    // 只有当上游API返回成功时才扣减次数
    if ($upstream_response['code'] === 200) {
        $cards[$card_index]['remaining_uses']--;
        $cards[$card_index]['last_used'] = date('Y-m-d H:i:s');
        saveCards($cards_file, $cards);

        // 添加剩余次数信息
        $upstream_response['remaining_uses'] = $cards[$card_index]['remaining_uses'];
    } else {
        // 查询失败，显示原始剩余次数
        $upstream_response['remaining_uses'] = $current_card['remaining_uses'];
    }
    
    // 记录日志
    logRequest($card_key, $name, $idcard, $lx, $upstream_response);
    
    // 返回响应
    echo json_encode($upstream_response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    $response = [
        'code' => $e->getCode() ?: 500,
        'message' => $e->getMessage()
    ];
    
    // 记录错误日志
    if (isset($card_key, $name, $idcard, $lx)) {
        logRequest($card_key ?? '', $name ?? '', $idcard ?? '', $lx ?? '', $response);
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
}
?>
