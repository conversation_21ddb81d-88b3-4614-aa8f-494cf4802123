<?php
header('Content-Type: application/json; charset=utf-8');

// 获取手机号参数
$phone = isset($_GET['phone']) ? trim($_GET['phone']) : '';

// 验证手机号
if (!preg_match('/^\d{11}$/', $phone)) {
    echo json_encode(['error' => '无效的手机号格式'], JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取手机归属地
function get_phone_location($phone) {
    $url = "http://cx.shouji.360.cn/phonearea.php?number=" . $phone;
    $response = file_get_contents($url);
    $data = json_decode($response, true);
    
    if ($data && $data['code'] == 0) {
        return [
            'province' => $data['data']['province'],
            'city' => $data['data']['city'],
            'operator' => $data['data']['sp']
        ];
    }
    return false;
}

// 获取经纬度
function get_coordinates($location, $retry = 3) {
    $url = "https://nominatim.openstreetmap.org/search?format=json&q=" . urlencode($location) . "&countrycodes=cn&limit=1";
    $options = [
        'http' => [
            'header' => [
                "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Accept: application/json",
                "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8",
                "Referer: https://www.openstreetmap.org/",
                "Origin: https://www.openstreetmap.org"
            ],
            'timeout' => 10
        ]
    ];
    
    for ($i = 0; $i < $retry; $i++) {
        try {
            $context = stream_context_create($options);
            $response = file_get_contents($url, false, $context);
            $data = json_decode($response, true);
            
            if ($data && count($data) > 0) {
                return [
                    'lon' => floatval($data[0]['lon']),
                    'lat' => floatval($data[0]['lat'])
                ];
            }
        } catch (Exception $e) {
            if ($i < $retry - 1) {
                sleep(2); // 等待2秒后重试
                continue;
            }
        }
    }
    return false;
}

// 获取详细地址信息
function get_street_info($lon, $lat, $retry = 3) {
    $url = "https://nominatim.openstreetmap.org/reverse?format=json&lat=" . $lat . "&lon=" . $lon . "&zoom=18&addressdetails=1";
    $options = [
        'http' => [
            'header' => [
                "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Accept: application/json",
                "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8",
                "Referer: https://www.openstreetmap.org/",
                "Origin: https://www.openstreetmap.org"
            ],
            'timeout' => 10
        ]
    ];
    
    for ($i = 0; $i < $retry; $i++) {
        try {
            $context = stream_context_create($options);
            $response = file_get_contents($url, false, $context);
            $data = json_decode($response, true);
            
            if ($data) {
                $address = $data['address'];
                return [
                    'province' => $address['state'] ?? '',
                    'city' => $address['city'] ?? '',
                    'county' => $address['county'] ?? '',
                    'town' => $address['town'] ?? '',
                    'road' => $address['road'] ?? '',
                    'neighbourhood' => $address['neighbourhood'] ?? '',
                    'village' => $address['village'] ?? '',
                    'house_number' => $address['house_number'] ?? '',
                    'building' => $address['building'] ?? '',
                    'residential' => $address['residential'] ?? '',
                    'landmark' => $address['landmark'] ?? ''
                ];
            }
        } catch (Exception $e) {
            if ($i < $retry - 1) {
                sleep(2); // 等待2秒后重试
                continue;
            }
        }
    }
    return false;
}

// 生成随机偏移
function random_offset($lon, $lat, $max_km = 20) {
    $angle = mt_rand() / mt_getrandmax() * 2 * M_PI;
    $distance_km = mt_rand() / mt_getrandmax() * $max_km;
    $earth_radius = 6371.0;
    
    $new_lat = $lat + ($distance_km / $earth_radius) * (180 / M_PI) * cos($angle);
    $new_lon = $lon + ($distance_km / $earth_radius) * (180 / M_PI) * sin($angle) / cos($lat * M_PI / 180);
    
    return [
        'lon' => round($new_lon, 6),
        'lat' => round($new_lat, 6)
    ];
}

// 主程序
$location_info = get_phone_location($phone);
if (!$location_info) {
    echo json_encode(['error' => '获取归属地失败'], JSON_UNESCAPED_UNICODE);
    exit;
}

$coordinates = get_coordinates($location_info['city']);
if (!$coordinates) {
    echo json_encode(['error' => '获取经纬度失败'], JSON_UNESCAPED_UNICODE);
    exit;
}

$offset_coords = random_offset($coordinates['lon'], $coordinates['lat']);
$street_info = get_street_info($offset_coords['lon'], $offset_coords['lat']);

// 生成必应地图URL
$bing_map_url = "https://www.bing.com/maps?cp=" . $offset_coords['lat'] . "~" . $offset_coords['lon'] . "&lvl=15";

// 组合结果
$result = [
    'province' => $location_info['province'],
    'city' => $location_info['city'],
    'town' => $street_info['town'] ?? '',
    'address' => implode(' ', array_filter([
        $street_info['province'],
        $street_info['city'],
        $street_info['county'],
        $street_info['town'],
        $street_info['road'],
        $street_info['neighbourhood'],
        $street_info['village'],
        $street_info['residential'],
        $street_info['landmark'],
        $street_info['building'],
        $street_info['house_number']
    ])),
    'coordinates' => [
        'longitude' => $offset_coords['lon'],
        'latitude' => $offset_coords['lat']
    ],
    'map_url' => $bing_map_url
];

echo json_encode($result, JSON_UNESCAPED_UNICODE);
