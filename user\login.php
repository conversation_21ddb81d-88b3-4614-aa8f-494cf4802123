<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录页面</title>

    <!-- 引入第三方库 -->
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.6.0/dist/sweetalert2.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.6.0/dist/sweetalert2.min.js"></script>
    
    <style>
        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background-color: #f0f0f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }

        .container {
            width: 100%;
            max-width: 400px;
            background-color: rgba(255, 255, 255, 0.85);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        h2 {
            text-align: center;
            color: #333;
            font-size: 1.5rem;
            margin-bottom: 20px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            font-size: 14px;
            margin-bottom: 5px;
            color: #555;
        }

        .input-group input {
            width: 100%;
            padding: 12px;
            font-size: 16px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background-color: #f9f9f9;
            box-sizing: border-box;
            outline: none;
            transition: 0.3s ease;
        }

        .input-group input:focus {
            border-color: #007bff;
        }

        .message-box {
            background-color: #f9f9f9;
            border: 1px solid #ccc;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 20px;
            color: #666;
        }

        .message-box a {
            color: #007bff;
        }

        .btn {
            display: block;
            width: 100%;
            padding: 15px;
            background-color: #007bff;
            color: white;
            font-size: 16px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .btn:hover {
            background-color: #0056b3;
        }

        .loading {
            display: none;
            margin: 0 auto;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>

<div class="container">
    <h2>立即登录</h2>

    <!-- 用户输入验证码 -->
    <div class="input-group">
        <label for="captcha">请输入验证码</label>
        <input type="text" id="captcha" name="captcha" placeholder="请输入验证码">
    </div>

    <!-- 展示如何获取验证码的内容 -->
    <div class="message-box">
        <p>如果您没有验证码，请访问 <a href="https://example.com/get-captcha" target="_blank">获取验证码</a> 页面。</p>
        <p>验证码有效期为5分钟。</p>
    </div>

    <!-- 登录按钮 -->
    <button class="btn" onclick="login()">立即登录</button>

    <!-- 加载动画 -->
    <div class="loading" id="loading"></div>
</div>

<script>
    let token = null;
    let loginStatus = 0;

    function login() {
        const captcha = document.getElementById('captcha').value;
        if (!captcha) {
            showToast('请输入验证码', 'error');
            return;
        }

        // 显示加载动画
        document.getElementById('loading').style.display = 'block';

        // 调用后台接口验证验证码
        fetch('logins.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `captcha=${captcha}`
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('loading').style.display = 'none'; // 隐藏加载动画

            if (data.code === 200) {
                // 登录成功
                showToast('登录成功，正在跳转到个人主页...', 'success', 1500);

                setTimeout(() => {
                    // 保存返回的 token 和登录状态
                    token = data.token;
                    sessionStorage.setItem('token', token);  // 存储 token
                    sessionStorage.setItem('loginStatus', 1);  // 存储登录状态为已登录

                    // 跳转到个人主页
                    window.location.href = "index.php";
                }, 1500);
            } else {
                showToast(data.message, 'error');
            }

        })
        .catch(error => {
            document.getElementById('loading').style.display = 'none'; // 隐藏加载动画
            showToast('网络错误，请稍后再试', 'error');
        });
    }

    function showToast(message, type, timer = 2500) {
        Swal.fire({
            position: 'top-end',
            icon: type,
            title: message,
            showConfirmButton: false,
            timer: timer,
            toast: true,
            background: 'rgba(0, 0, 0, 0.7)',
            color: '#fff',
            padding: '15px',
            fontSize: '16px',
            showClass: {
                popup: 'animate__animated animate__fadeInRight'
            },
            hideClass: {
                popup: 'animate__animated animate__fadeOutRight'
            }
        });
    }
</script>

</body>
</html>
