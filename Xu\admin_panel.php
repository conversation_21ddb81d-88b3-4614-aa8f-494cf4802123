<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理系统</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        :root {
            --glass-bg: rgba(255, 255, 255, 0.7);
            --glass-border: rgba(255, 255, 255, 0.3);
            --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: var(--glass-shadow);
            background: rgba(52, 58, 64, 0.9);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border-right: 1px solid var(--glass-border);
        }
        
        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }
        
        .nav-link {
            color: #fff;
            padding: 1rem;
            transition: all 0.3s ease;
            border-radius: 10px;
            margin: 0.5rem 1rem;
        }
        
        .nav-link:hover {
            color: #fff;
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }
        
        .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .main-content {
            margin-left: 240px;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: var(--glass-shadow);
            background: var(--glass-bg);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border: 1px solid var(--glass-border);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .card-header {
            background: linear-gradient(135deg, rgba(52, 58, 64, 0.9) 0%, rgba(73, 80, 87, 0.9) 100%);
            color: white;
            padding: 1.5rem;
            border-bottom: 1px solid var(--glass-border);
        }
        
        .table {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            overflow: hidden;
        }
        
        .table th {
            background: rgba(52, 58, 64, 0.9);
            color: white;
            border: none;
        }
        
        .table td {
            border-color: var(--glass-border);
            vertical-align: middle;
        }
        
        .form-control, .form-select {
            border-radius: 12px;
            padding: 0.75rem 1rem;
            border: 1px solid var(--glass-border);
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.25);
            border-color: #343a40;
            background: rgba(255, 255, 255, 1);
        }
        
        .btn {
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #343a40 0%, #495057 100%);
            border: none;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;
        }
        
        .notice {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid var(--glass-border);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }
        
        .notice h4 {
            color: #dc3545;
            margin-bottom: 15px;
        }
        
        .notice p {
            margin-bottom: 10px;
            color: #495057;
        }
        
        .notice a {
            color: #0d6efd;
            text-decoration: none;
        }
        
        .notice a:hover {
            text-decoration: underline;
        }
        
        .animate__animated {
            animation-duration: 0.5s;
        }
        
        .section {
            animation: fadeIn 0.5s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <nav class="sidebar" style="width: 240px;">
        <div class="sidebar-sticky">
            <div class="notice mb-4 mx-3">
                <h4><i class="fas fa-exclamation-triangle me-2"></i>重要公告</h4>
                <p>本程序由泪绪团队制作 ⚠️</p>
                <p>- 作者：泪绪@qingxu6nb</p>
                <p>- 官方唯一授权售卖渠道：泪绪 <a href="https://t.me/qingxu6nb666" target="_blank">https://t.me/qingxu6nb666</a></p>
                <p class="text-danger"><i class="fas fa-exclamation-circle me-2"></i>❗️ 严禁倒卖，泛滥，镜像，一旦发现立即封卡 ❗️</p>
            </div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="#" onclick="showSection('users')">
                        <i class="fas fa-users me-2"></i>用户管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="showSection('add-user')">
                        <i class="fas fa-user-plus me-2"></i>添加用户
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="showSection('recharge')">
                        <i class="fas fa-credit-card me-2"></i>充值管理
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <!-- 用户管理部分 -->
        <div id="users" class="section">
            <h2 class="mb-4">用户管理</h2>
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>Token</th>
                                    <th>剩余查询次数</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="usersList">
                                <!-- 用户列表将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加用户部分 -->
        <div id="add-user" class="section" style="display: none;">
            <h2 class="mb-4">添加用户</h2>
            <div class="card">
                <div class="card-body">
                    <form id="addUserForm">
                        <div class="mb-3">
                            <label for="username" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="queries" class="form-label">初始查询次数</label>
                            <input type="number" class="form-control" id="queries" required min="1">
                        </div>
                        <button type="submit" class="btn btn-primary">添加用户</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- 充值管理部分 -->
        <div id="recharge" class="section" style="display: none;">
            <h2 class="mb-4">充值管理</h2>
            <div class="card">
                <div class="card-body">
                    <form id="rechargeForm">
                        <div class="mb-3">
                            <label for="rechargeUsername" class="form-label">用户名</label>
                            <select class="form-select" id="rechargeUsername" required>
                                <!-- 用户列表将通过JavaScript动态加载 -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="rechargeAmount" class="form-label">充值次数</label>
                            <input type="number" class="form-control" id="rechargeAmount" required min="1">
                        </div>
                        <button type="submit" class="btn btn-primary">确认充值</button>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 显示/隐藏不同部分
        function showSection(sectionId) {
            document.querySelectorAll('.section').forEach(section => {
                section.style.display = 'none';
            });
            document.getElementById(sectionId).style.display = 'block';
            
            // 更新导航栏活动状态
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // 加载用户列表
        async function loadUsers() {
            try {
                const response = await fetch('admin.php?action=list_users');
                const data = await response.json();
                if (data.code === 200) {
                    const usersList = document.getElementById('usersList');
                    const rechargeSelect = document.getElementById('rechargeUsername');
                    
                    usersList.innerHTML = '';
                    rechargeSelect.innerHTML = '';
                    
                    data.users.forEach(user => {
                        // 更新用户列表表格
                        usersList.innerHTML += `
                            <tr>
                                <td>${user.username}</td>
                                <td>${user.token}</td>
                                <td>${user.queries_left}</td>
                                <td>
                                    <button class="btn btn-danger btn-sm" onclick="deleteUser('${user.username}')">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </td>
                            </tr>
                        `;
                        
                        // 更新充值下拉框
                        rechargeSelect.innerHTML += `
                            <option value="${user.username}">${user.username} (剩余: ${user.queries_left}次)</option>
                        `;
                    });
                }
            } catch (error) {
                alert('加载用户列表失败');
            }
        }

        // 添加用户
        document.getElementById('addUserForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const username = document.getElementById('username').value;
            const queries = document.getElementById('queries').value;
            
            try {
                const response = await fetch('admin.php?action=add_user', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, queries: parseInt(queries) })
                });
                
                const data = await response.json();
                if (data.code === 200) {
                    alert(`用户添加成功！Token: ${data.token}`);
                    document.getElementById('addUserForm').reset();
                    loadUsers();
                } else {
                    alert(data.message);
                }
            } catch (error) {
                alert('添加用户失败');
            }
        });

        // 充值
        document.getElementById('rechargeForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const username = document.getElementById('rechargeUsername').value;
            const queries = document.getElementById('rechargeAmount').value;
            
            try {
                const response = await fetch('admin.php?action=recharge', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, queries: parseInt(queries) })
                });
                
                const data = await response.json();
                if (data.code === 200) {
                    alert('充值成功！');
                    document.getElementById('rechargeForm').reset();
                    loadUsers();
                } else {
                    alert(data.message);
                }
            } catch (error) {
                alert('充值失败');
            }
        });

        // 删除用户
        async function deleteUser(username) {
            if (!confirm(`确定要删除用户 ${username} 吗？`)) return;
            
            try {
                const response = await fetch('admin.php?action=delete_user', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username })
                });
                
                const data = await response.json();
                if (data.code === 200) {
                    alert('用户删除成功！');
                    loadUsers();
                } else {
                    alert(data.message);
                }
            } catch (error) {
                alert('删除用户失败');
            }
        }

        // 页面加载时加载用户列表
        loadUsers();
    </script>
</body>
</html> 