<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>身份证库补 - 模糊补全</title>

    <!-- 引入 Tailwind CSS（通过 CDN） -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">

    <!-- 引入 Ionicons（通过 CDN） -->
    <link href="https://cdn.jsdelivr.net/npm/ionicons@5.5.2/dist/ionicons.css" rel="stylesheet">
</head>
<body class="bg-gray-100 font-sans">

    <!-- 容器：最大宽度 500px，居中显示 -->
    <div class="max-w-md mx-auto mt-20 bg-white p-6 rounded-2xl shadow-lg">
        <h1 class="text-3xl font-semibold text-center text-gray-900 mb-6">身份证库补 - 模糊补全</h1>

        <!-- 输入表单 -->
        <div class="mb-4">
            <label for="token" class="block text-gray-700 text-sm font-medium">Token</label>
            <input type="text" id="token" placeholder="请输入您的Token"
                class="w-full mt-2 p-3 bg-gray-100 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <div class="mb-4">
            <label for="name" class="block text-gray-700 text-sm font-medium">姓名</label>
            <input type="text" id="name" placeholder="请输入姓名"
                class="w-full mt-2 p-3 bg-gray-100 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <div class="mb-4">
            <label for="idcard" class="block text-gray-700 text-sm font-medium">模糊身份证号</label>
            <input type="text" id="idcard" placeholder="请输入模糊的身份证号（x代表模糊位）"
                class="w-full mt-2 p-3 bg-gray-100 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <!-- 补齐按钮 -->
        <button id="queryBtn" class="w-full bg-blue-600 text-white py-3 rounded-lg text-lg flex items-center justify-center hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300"
            onclick="queryData()">
            <ion-icon name="search-outline" class="mr-2 text-xl"></ion-icon>
            补齐
        </button>

        <!-- 加载动画：显示补齐中的效果 -->
        <div id="loading" class="hidden text-center mt-4">
            <ion-icon name="reload-circle" class="animate-spin text-4xl text-blue-600"></ion-icon>
            <p class="mt-2 text-gray-700">补齐中...</p>
        </div>

        <!-- 补齐结果展示区域 -->
        <div class="mt-6 bg-gray-50 p-4 rounded-lg shadow-md" id="result" style="display: none;">
            <h3 class="font-semibold text-lg text-gray-800">补齐结果：</h3>
            <p id="result-message" class="text-sm text-gray-600"></p>
            <ul id="result-msg" class="text-sm text-gray-600">
                <!-- 补齐到的数据将显示在这里 -->
            </ul>
            <p class="text-blue-600 mt-4">官方频道：<a href="https://t.me/kmhsgk" target="_blank">@kmhsgk</a></p>
            <p class="text-blue-600">官网链接：<a href="https://qnm8.top/" target="_blank">https://qnm8.top/</a></p>
        </div>
    </div>

    <script>
        // 获取输入数据并调用 API
        function queryData() {
            const token = document.getElementById('token').value;
            const name = document.getElementById('name').value;
            const idcard = document.getElementById('idcard').value;

            // 检查输入字段是否为空
            if (!token || !name || !idcard) {
                alert('请填写所有字段');
                return;
            }

            // 禁用按钮并显示加载动画
            document.getElementById('queryBtn').disabled = true;
            document.getElementById('loading').classList.remove('hidden');
            document.getElementById('result').style.display = 'none';

            const url = `https://qnm8.top/api/kb?token=${token}&name=${name}&idcard=${idcard}`;

            // 使用 fetch API 获取数据
            // 使用 fetch API 获取数据
fetch(url)
    .then(response => response.json())
    .then(data => {
        // 补齐完成后，恢复按钮状态并隐藏加载动画
        document.getElementById('queryBtn').disabled = false;
        document.getElementById('loading').classList.add('hidden');

        if (data.code === 200) {
            // 显示补齐结果
            document.getElementById('result').style.display = 'block';
            document.getElementById('result-message').textContent = data.message;

            // 假设每个 msg 项是一个对象，显示其某个属性，比如 name 和 idcard
            const resultMsg = data.msg.map(item => {
                // 假设 item 对象中包含 `name` 和 `idcard` 字段
                return `<li>Name: ${item.name}, ID Card: ${item.idcard}</li>`;
            }).join('');
            document.getElementById('result-msg').innerHTML = resultMsg;
        } else {
            alert('补齐失败: ' + data.message);
        }
    })
    .catch(error => {
        // 补齐失败后，恢复按钮状态并隐藏加载动画
        document.getElementById('queryBtn').disabled = false;
        document.getElementById('loading').classList.add('hidden');
        alert('请求失败: ' + error.message);
    });

        }
    </script>

</body>
</html>
