<?php include 'includes/header.php'; ?>

<div class="card">
    <div class="header">
        <h1>🔍 Q绑查询 <small class="text-muted">QQ Binding</small></h1>
        <p class="text-muted">输入QQ号码，查询该QQ绑定的手机号信息 📱</p>
    </div>

    <form class="api-form" id="qqForm">
        <div class="form-group">
            <label for="token" class="form-label">Token</label>
            <input type="text" class="form-control" id="token" name="token" required placeholder="请输入您的Token">
        </div>

        <div class="form-group">
            <label for="qq" class="form-label">QQ号码</label>
            <input type="text" class="form-control" id="qq" name="qq" required placeholder="请输入QQ号码" pattern="[0-9]{5,13}">
        </div>

        <div class="form-group">
            <button type="submit" class="btn btn-primary">
                <i class="bi bi-search"></i> 开始查询
            </button>
        </div>

        <div class="loading">查询中，请稍候...</div>
        <div class="media-preview"></div>
        <pre class="result-area" style="display: none;"></pre>
    </form>
</div>

<script>
document.getElementById('qqForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const token = document.getElementById('token').value.trim();
    const qq = document.getElementById('qq').value.trim();
    const loading = document.querySelector('.loading');
    const resultArea = document.querySelector('.result-area');
    const submitBtn = document.querySelector('button[type="submit"]');

    // 验证输入
    if (!token) {
        alert('请输入Token');
        return;
    }

    if (!qq) {
        alert('请输入QQ号码');
        return;
    }

    if (!/^\d{5,13}$/.test(qq)) {
        alert('请输入有效的QQ号码（5-13位数字）');
        return;
    }

    // 显示加载状态
    loading.style.display = 'block';
    resultArea.style.display = 'none';
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 查询中...';

    try {
        // 调用API
        const response = await fetch(`extend/api/qq/index.php?token=${encodeURIComponent(token)}&qq=${encodeURIComponent(qq)}`);
        const result = await response.json();

        // 显示结果
        resultArea.textContent = JSON.stringify(result, null, 2);
        resultArea.style.display = 'block';

        // 如果查询成功，格式化显示
        if (result.code === 200) {
            const formattedResult = `查询结果：
${result.shuju}

执行时间：${result.execution_time}
频道：${result.频道}
官网：${result.官网}`;
            resultArea.textContent = formattedResult;
        }

    } catch (error) {
        console.error('查询失败:', error);
        resultArea.textContent = `查询失败：${error.message}`;
        resultArea.style.display = 'block';
    } finally {
        // 恢复按钮状态
        loading.style.display = 'none';
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="bi bi-search"></i> 开始查询';
    }
});

// QQ号输入验证
document.getElementById('qq').addEventListener('input', function(e) {
    let value = e.target.value.replace(/[^\d]/g, '');
    if (value.length > 13) {
        value = value.slice(0, 13);
    }
    e.target.value = value;
});

// Token输入处理
document.getElementById('token').addEventListener('input', function(e) {
    e.target.value = e.target.value.trim();
});
</script>

<?php include 'includes/footer.php'; ?>