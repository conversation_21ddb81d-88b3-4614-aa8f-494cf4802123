import os
import cv2
from deepface import DeepFace
import shutil

# 定义路径
base_folder = './假头数据'  # 假头数据目录
male_folder = './男'  # 男性目录
female_folder = './女'  # 女性目录

# 定义年龄段
age_groups = {
    '10-20岁': range(10, 21),   # 10-20岁
    '20-30岁': range(20, 31),   # 20-30岁
    '30-40岁': range(30, 41),   # 30-40岁
    '40-50岁': range(40, 51),   # 40-50岁
    '50-60岁': range(50, 61),   # 50-60岁
    '60-70岁': range(60, 71),   # 60-70岁
    '70-80岁': range(70, 81),   # 70-80岁
    '80-90岁': range(80, 91),   # 80-90岁
}

# 创建目录
for gender_folder in [male_folder, female_folder]:
    os.makedirs(gender_folder, exist_ok=True)
    for age_group in age_groups.keys():
        os.makedirs(os.path.join(gender_folder, age_group), exist_ok=True)

# 遍历假头数据文件夹中的所有图片
for filename in os.listdir(base_folder):
    if filename.endswith('.jpg') or filename.endswith('.png'):
        img_path = os.path.join(base_folder, filename)
        img = cv2.imread(img_path)

        try:
            # 使用 DeepFace 分析性别和年龄
            analysis = DeepFace.analyze(img, actions=['age', 'gender'])
            age = analysis[0]['age']
            gender = analysis[0]['gender']

            # 判断性别和年龄组
            if gender == 'Man':
                target_folder = male_folder
            else:
                target_folder = female_folder

            # 找到对应的年龄段
            for group, age_range in age_groups.items():
                if age in age_range:
                    # 移动文件到目标目录
                    target_path = os.path.join(target_folder, group, filename)
                    shutil.move(img_path, target_path)
                    print(f"Moved {filename} to {target_folder}/{group}")
                    break

        except Exception as e:
            print(f"Error processing {filename}: {e}")

print("处理完成！")
