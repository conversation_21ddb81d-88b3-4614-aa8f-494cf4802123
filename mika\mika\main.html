<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>领头Yang工作室 - 司法信息查询系统</title>
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1a237e;
            --secondary-color: #0d47a1;
            --background-color: #f5f5f5;
            --card-background: #ffffff;
            --text-primary: #333333;
            --text-secondary: #666666;
            --border-radius: 12px;
            --spacing-unit: 16px;
        }

        body {
            font-family: "SF Pro Display", "Microsoft YaHei", "SimHei", sans-serif;
            background-color: var(--background-color);
            margin: 0;
            padding: 0;
            color: var(--text-primary);
            line-height: 1.5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: var(--spacing-unit);
        }

        .header {
            text-align: center;
            padding: calc(var(--spacing-unit) * 2) 0;
            margin-bottom: calc(var(--spacing-unit) * 2);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: var(--border-radius);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            animation: fadeIn 1s ease-out;
        }

        .header h1 {
            font-size: 28px;
            margin: 0;
            font-weight: 600;
        }

        .header p {
            font-size: 16px;
            margin: 8px 0 0;
            opacity: 0.9;
        }

        .national-spirit {
            background: var(--card-background);
            padding: calc(var(--spacing-unit) * 1.5);
            margin: calc(var(--spacing-unit) * 2) 0;
            border-radius: var(--border-radius);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            border-left: 4px solid #e60012;
            animation: slideInLeft 0.5s ease-out;
        }

        .national-spirit h3 {
            color: #e60012;
            margin: 0 0 12px;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .national-spirit p {
            margin: 8px 0;
            line-height: 1.6;
            color: var(--text-secondary);
        }

        .form-section {
            background: var(--card-background);
            padding: calc(var(--spacing-unit) * 1.5);
            margin-bottom: calc(var(--spacing-unit) * 2);
            border-radius: var(--border-radius);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            animation: fadeInUp 0.5s ease-out;
        }

        .form-section h2 {
            color: var(--primary-color);
            font-size: 20px;
            margin: 0 0 20px;
            text-align: center;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: calc(var(--spacing-unit) * 1.5);
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(0,0,0,0.1);
            border-radius: var(--border-radius);
            font-size: 16px;
            box-sizing: border-box;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(26,35,126,0.1);
            outline: none;
        }

        .radio-group {
            display: flex;
            gap: calc(var(--spacing-unit) * 1.5);
            margin-bottom: var(--spacing-unit);
        }

        .radio-option {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 14px 24px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            font-weight: 500;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(26,35,126,0.2);
        }

        .btn:active {
            transform: translateY(1px);
        }

        .disclaimer {
            background: #fff5f5;
            padding: calc(var(--spacing-unit) * 1.5);
            margin-top: calc(var(--spacing-unit) * 2);
            border-radius: var(--border-radius);
            font-size: 14px;
            color: var(--text-secondary);
            border: 1px solid #ffe0e0;
        }

        .disclaimer h4 {
            color: #e60012;
            margin: 0 0 12px;
            font-size: 16px;
        }

        .disclaimer p {
            margin: 8px 0;
            line-height: 1.6;
        }

        .loading {
            text-align: center;
            padding: var(--spacing-unit);
            display: none;
        }

        .result {
            margin-top: var(--spacing-unit);
            padding: var(--spacing-unit);
            background: var(--card-background);
            border-radius: var(--border-radius);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .result img {
            max-width: 100%;
            border-radius: calc(var(--border-radius) / 2);
        }

        .contact-section {
            margin-top: calc(var(--spacing-unit) * 2);
            padding: calc(var(--spacing-unit) * 1.5);
            background: var(--card-background);
            border-radius: var(--border-radius);
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .contact-section h3 {
            color: var(--primary-color);
            margin-bottom: var(--spacing-unit);
            font-size: 18px;
        }

        .telegram-links {
            display: flex;
            justify-content: center;
            gap: var(--spacing-unit);
            margin-top: var(--spacing-unit);
            flex-wrap: wrap;
        }

        .telegram-link {
            display: inline-flex;
            align-items: center;
            padding: 12px 24px;
            background-color: #0088cc;
            color: white;
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .telegram-link:hover {
            background-color: #006699;
            transform: translateY(-1px);
        }

        .telegram-link img {
            width: 20px;
            height: 20px;
            margin-right: 8px;
        }

        @media (max-width: 768px) {
            .container {
                padding: calc(var(--spacing-unit) / 2);
            }

            .header {
                padding: var(--spacing-unit);
            }

            .radio-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header animate__animated animate__fadeIn">
            <h1>领头Yang工作室司法信息查询系统</h1>
            <p>司法信息查询服务平台</p>
        </div>

        <div class="national-spirit animate__animated animate__fadeInLeft">
            <h3>🇨🇳 爱国宣言</h3>
            <p>相信党相信国家相信政府相信自己，关注人民群众的根本利益，理解人民群众的文化需求，从人民群众的实践中汲取营养。反对"守旧主义"和"封闭主义"以及"民族虚无主义"和"历史虚无主义"倾向。增强全民族文化创造活力，推动文化内容形式、体制机制和传播手段创新。</p>
        </div>
        
        <div class="form-section animate__animated animate__fadeInUp">
            <h2>抖音号查询身份信息</h2>
            <form id="douyinForm">
                <div class="form-group">
                    <label for="douyin">抖音号</label>
                    <input type="text" id="douyin" name="douyin" class="form-control" required 
                           placeholder="请输入抖音号">
                </div>
                <button type="submit" class="btn">查询身份信息</button>
            </form>
            <div class="result" id="douyinResult"></div>
        </div>

        <div class="form-section animate__animated animate__fadeInUp">
            <h2>快手号查询身份信息</h2>
            <form id="kuaishouForm">
                <div class="form-group">
                    <label for="kuaishou">快手号</label>
                    <input type="text" id="kuaishou" name="kuaishou" class="form-control" required 
                           placeholder="请输入快手号">
                </div>
                <button type="submit" class="btn">查询身份信息</button>
            </form>
            <div class="result" id="kuaishouResult"></div>
        </div>

        <div class="form-section animate__animated animate__fadeInUp">
            <h2>微信号查询身份信息</h2>
            <form id="wechatForm">
                <div class="form-group">
                    <label for="wechat">微信号</label>
                    <input type="text" id="wechat" name="wechat" class="form-control" required 
                           placeholder="请输入微信号">
                </div>
                <button type="submit" class="btn">查询身份信息</button>
            </form>
            <div class="result" id="wechatResult"></div>
        </div>

        <div class="form-section animate__animated animate__fadeInUp">
            <h2>婚姻史查询</h2>
            <form id="marriageForm">
                <div class="form-group">
                    <label for="marriageName">姓名</label>
                    <input type="text" id="marriageName" name="name" class="form-control" required 
                           placeholder="请输入姓名">
                </div>
                <div class="form-group">
                    <label for="marriageIdcard">身份证号码</label>
                    <input type="text" id="marriageIdcard" name="idcard" class="form-control" required 
                           placeholder="请输入18位身份证号码" maxlength="18">
                </div>
                <button type="submit" class="btn">查询婚姻史</button>
            </form>
            <div class="result" id="marriageResult"></div>
        </div>

        <div class="form-section animate__animated animate__fadeInUp">
            <h2>信息查询</h2>
            <form id="queryForm">
                <div class="form-group">
                    <label>查询类型</label>
                    <div class="radio-group">
                        <div class="radio-option">
                            <input type="radio" id="type1" name="lx" value="1" checked>
                            <label for="type1">民事诉讼信息查询</label>
                        </div>
                        <div class="radio-option">
                            <input type="radio" id="type2" name="lx" value="2">
                            <label for="type2">户籍信息查询</label>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="name">姓名</label>
                    <input type="text" id="name" name="name" class="form-control" required 
                           placeholder="请输入真实姓名">
                </div>
                
                <div class="form-group">
                    <label for="idcard">身份证号码</label>
                    <input type="text" id="idcard" name="idcard" class="form-control" required 
                           placeholder="请输入18位身份证号码" maxlength="18">
                </div>
                
                <button type="submit" class="btn">查询</button>
            </form>
        </div>
        
       
        
        <div class="loading" id="loading">
            正在查询中，请稍候...
        </div>
        
        <div class="result" id="result">
            <!-- 查询结果将在这里显示 -->
        </div>

        <div class="form-section animate__animated animate__fadeInUp">
            <h2>综合社工查询</h2>
            <form id="sgzhForm">
                <div class="form-group">
                    <label for="sgzh">查询内容</label>
                    <input type="text" id="sgzh" name="sgzh" class="form-control" required 
                           placeholder="请输入查询内容">
                </div>
                <button type="submit" class="btn">查询</button>
            </form>
            <div class="result" id="sgzhResult"></div>
        </div>

        <div class="form-section animate__animated animate__fadeInUp">
            <h2>全户处理查询</h2>
            <form id="familyForm">
                <div class="form-group">
                    <label for="familyName">姓名</label>
                    <input type="text" id="familyName" name="name" class="form-control" required 
                           placeholder="请输入姓名">
                </div>
                <div class="form-group">
                    <label for="familyIdcard">身份证号码</label>
                    <input type="text" id="familyIdcard" name="idcard" class="form-control" required 
                           placeholder="请输入18位身份证号码" maxlength="18">
                </div>
                <button type="submit" class="btn">查询全户信息</button>
            </form>
            <div class="result" id="familyResult"></div>
        </div>

        <div class="form-section animate__animated animate__fadeInUp">
            <h2>真实地址查询</h2>
            <form id="addressForm">
                <div class="form-group">
                    <label for="addressName">姓名</label>
                    <input type="text" id="addressName" name="name" class="form-control" required 
                           placeholder="请输入姓名">
                </div>
                <div class="form-group">
                    <label for="addressIdcard">身份证号码</label>
                    <input type="text" id="addressIdcard" name="idcard" class="form-control" required 
                           placeholder="请输入18位身份证号码" maxlength="18">
                </div>
                <button type="submit" class="btn">查询真实地址</button>
            </form>
            <div class="result" id="addressResult"></div>
        </div>

        <div class="disclaimer animate__animated animate__fadeIn">
            <h4>重要声明</h4>
            <p>★一，本软件只提供学习和交流使用，不得使用本软件违反国家法律法规和非法广告信息，如色情，赌博，骚扰，诈骗，威胁等，其造成的一切后果与本作者无关，由使用者自行负责，请自觉营造和谐良性的网络环境。违法行为一经发现，本作者有权终止服务并追究法律责任。</p>
            <p>★二，使用本软件产品风险由用户自行承担，在适用法律允许的最大范围内，对因使用或不能使用本软件所产生的损害及风险，包括但不限于直接或间接的个人损害、商业赢利的丧失、贸易中断、商业信息的丢失或任何其它经济损失，作者不承担任何责任。</p>
            <p>★三，对于因电信系统或互联网网络故障、手机故障或病毒、信息损坏或丢失、手机系统问题或其它任何不可抗力原因而产生损失，作者不承担任何责任。</p>
            <p>★四，用户违反本协议规定，对作者公司或作者个人造成损害的。作者有权采取包括但不限于中断使用许可、停止提供服务、限制使用、法律追究等措施。</p>
            <p>★五，由于不可抗之力如地震，火山爆发，自然灾害，战争，或本软件开发者实在无力续费服务器等原因，造成本软件和相关软件最终停止服务的情况下，本软件相关开发人员不做任何赔偿和解释！</p>
        </div>

        <div class="contact-section animate__animated animate__fadeIn">
            <h3>联系方式</h3>
            <p>如需帮助或咨询，请通过以下方式联系我们：</p>
            <div class="telegram-links">
                <a href="https://t.me/LoveChinaOpenfileprofessional" class="telegram-link" target="_blank">
                    <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTExLjk5NCAwQzUuMzc4IDAgMCA1LjM3OCAwIDExLjk5NCA0IDE4LjYxIDUuMzc4IDI0IDExLjk5NCAyNCAxOC42MSAyNCAyNCAxOC42MSAyNCAxMS45OTQgMjQgNS4zNzggMTguNjEgMCAxMS45OTQgMHptNS4yMzYgOC4yNDRjLjM0NSAwIC42ODguMTM0Ljk1LjM5Ni4yNjIuMjYyLjM5Ni42MDUuMzk2Ljk1IDAgLjM0NS0uMTM0LjY4OC0uMzk2Ljk1bC0yLjI3IDIuMjcgMi4yNyAyLjI3Yy4yNjIuMjYyLjM5Ni42MDUuMzk2Ljk1IDAgLjM0NS0uMTM0LjY4OC0uMzk2Ljk1LS4yNjIuMjYyLS42MDUuMzk2LS45NS4zOTYtLjM0NSAwLS42ODgtLjEzNC0uOTUtLjM5NmwtMi4yNy0yLjI3LTIuMjcgMi4yN2MtLjI2Mi4yNjItLjYwNS4zOTYtLjk1LjM5Ni0uMzQ1IDAtLjY4OC0uMTM0LS45NS0uMzk2LS4yNjItLjI2Mi0uMzk2LS42MDUtLjM5Ni0uOTUgMC0uMzQ1LjEzNC0uNjg4LjM5Ni0uOTVsMi4yNy0yLjI3LTIuMjctMi4yN2MtLjI2Mi0uMjYyLS4zOTYtLjYwNS0uMzk2LS45NSAwLS4zNDUuMTM0LS42ODguMzk2LS45NS4yNjItLjI2Mi42MDUtLjM5Ni45NS0uMzk2LjM0NSAwIC42ODguMTM0Ljk1LjM5NmwyLjI3IDIuMjcgMi4yNy0yLjI3Yy4yNjItLjI2Mi42MDUtLjM5Ni45NS0uMzk2eiIvPjwvc3ZnPg==" alt="Telegram">
                    加入群组
                </a>
                <a href="https://t.me/Zhongyangqingbaojubot" class="telegram-link" target="_blank">
                    <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTExLjk5NCAwQzUuMzc4IDAgMCA1LjM3OCAwIDExLjk5NCA0IDE4LjYxIDUuMzc4IDI0IDExLjk5NCAyNCAxOC42MSAyNCAyNCAxOC42MSAyNCAxMS45OTQgMjQgNS4zNzggMTguNjEgMCAxMS45OTQgMHptNS4yMzYgOC4yNDRjLjM0NSAwIC42ODguMTM0Ljk1LjM5Ni4yNjIuMjYyLjM5Ni42MDUuMzk2Ljk1IDAgLjM0NS0uMTM0LjY4OC0uMzk2Ljk1bC0yLjI3IDIuMjcgMi4yNyAyLjI3Yy4yNjIuMjYyLjM5Ni42MDUuMzk2Ljk1IDAgLjM0NS0uMTM0LjY4OC0uMzk2Ljk1LS4yNjIuMjYyLS42MDUuMzk2LS45NS4zOTYtLjM0NSAwLS42ODgtLjEzNC0uOTUtLjM5NmwtMi4yNy0yLjI3LTIuMjcgMi4yN2MtLjI2Mi4yNjItLjYwNS4zOTYtLjk1LjM5Ni0uMzQ1IDAtLjY4OC0uMTM0LS45NS0uMzk2LS4yNjItLjI2Mi0uMzk2LS42MDUtLjM5Ni0uOTUgMC0uMzQ1LjEzNC0uNjg4LjM5Ni0uOTVsMi4yNy0yLjI3LTIuMjctMi4yN2MtLjI2Mi0uMjYyLS4zOTYtLjYwNS0uMzk2LS45NSAwLS4zNDUuMTM0LS42ODguMzk2LS45NS4yNjItLjI2Mi42MDUtLjM5Ni45NS0uMzk2LjM0NSAwIC42ODguMTM0Ljk1LjM5NmwyLjI3IDIuMjcgMi4yNy0yLjI3Yy4yNjItLjI2Mi42MDUtLjM5Ni45NS0uMzk2eiIvPjwvc3ZnPg==" alt="Telegram">
                    联系机器人
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css"></script>
    <script>
        document.getElementById('queryForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const name = document.getElementById('name').value;
            const idcard = document.getElementById('idcard').value;
            const lx = document.querySelector('input[name="lx"]:checked').value;
            
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').innerHTML = '';
            
            fetch(`mika.php?name=${encodeURIComponent(name)}&idcard=${encodeURIComponent(idcard)}&lx=${lx}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('loading').style.display = 'none';
                    if (data.code === 200) {
                        document.getElementById('result').innerHTML = `
                            <img src="${data.data.image_url}" alt="查询结果">
                        `;
                    } else {
                        document.getElementById('result').innerHTML = `
                            <p style="color: red;">${data.message}</p>
                        `;
                    }
                })
                .catch(error => {
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('result').innerHTML = `
                        <p style="color: red;">查询失败，请稍后重试</p>
                    `;
                });
        });

        document.getElementById('wechatForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const wechat = document.getElementById('wechat').value;
            
            fetch(`mika.php?action=generate_id&wechat=${encodeURIComponent(wechat)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        document.getElementById('wechatResult').innerHTML = `
                            <pre>姓名：${data.data.name}
身份证号：${data.data.idcard}</pre>
                        `;
                    } else {
                        document.getElementById('wechatResult').innerHTML = `
                            <p style="color: red;">${data.message}</p>
                        `;
                    }
                })
                .catch(error => {
                    document.getElementById('wechatResult').innerHTML = `
                        <p style="color: red;">查询失败，请稍后重试</p>
                    `;
                });
        });

        document.getElementById('marriageForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const name = document.getElementById('marriageName').value;
            const idcard = document.getElementById('marriageIdcard').value;
            
            fetch(`mika.php?action=generate_marriage&name=${encodeURIComponent(name)}&idcard=${encodeURIComponent(idcard)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        let resultHtml = `
                            <pre>姓名：${data.data.姓名}
身份证号：${data.data.身份证号}
婚姻状况：${data.data.婚姻状况}
婚姻次数：${data.data.婚姻次数}次

婚姻史：`;
                        
                        data.data.婚姻史.forEach((marriage, index) => {
                            resultHtml += `
第${index + 1}段婚姻：
    配偶姓名：${marriage.配偶姓名}
    登记时间：${marriage.登记时间}
    离婚时间：${marriage.离婚时间}
    离婚原因：${marriage.离婚原因}
    子女情况：${marriage.子女情况}
`;
                        });
                        
                        resultHtml += '</pre>';
                        document.getElementById('marriageResult').innerHTML = resultHtml;
                    } else {
                        document.getElementById('marriageResult').innerHTML = `
                            <p style="color: red;">${data.message}</p>
                        `;
                    }
                })
                .catch(error => {
                    document.getElementById('marriageResult').innerHTML = `
                        <p style="color: red;">查询失败，请稍后重试</p>
                    `;
                });
        });

        document.getElementById('douyinForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const douyin = document.getElementById('douyin').value;
            
            fetch(`mika.php?action=generate_id&douyin=${encodeURIComponent(douyin)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        document.getElementById('douyinResult').innerHTML = `
                            <pre>姓名：${data.data.name}
身份证号：${data.data.idcard}</pre>
                        `;
                    } else {
                        document.getElementById('douyinResult').innerHTML = `
                            <p style="color: red;">${data.message}</p>
                        `;
                    }
                })
                .catch(error => {
                    document.getElementById('douyinResult').innerHTML = `
                        <p style="color: red;">查询失败，请稍后重试</p>
                    `;
                });
        });

        // 通用JSON格式化函数
        function formatJsonToHtml(obj, level = 0) {
            const indent = '    '.repeat(level);
            let html = '';
            
            if (typeof obj === 'object' && obj !== null) {
                if (Array.isArray(obj)) {
                    obj.forEach((item, index) => {
                        if (typeof item === 'object' && item !== null) {
                            html += `${indent}${index + 1}:\n${formatJsonToHtml(item, level + 1)}`;
                        } else {
                            html += `${indent}${index + 1}: ${item}\n`;
                        }
                    });
                } else {
                    for (const key in obj) {
                        if (typeof obj[key] === 'object' && obj[key] !== null) {
                            html += `${indent}${key}:\n${formatJsonToHtml(obj[key], level + 1)}`;
                        } else {
                            html += `${indent}${key}: ${obj[key]}\n`;
                        }
                    }
                }
            } else {
                html += `${indent}${obj}\n`;
            }
            
            return html;
        }

        document.getElementById('sgzhForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const sgzh = document.getElementById('sgzh').value;
            
            document.getElementById('loading').style.display = 'block';
            document.getElementById('sgzhResult').innerHTML = '';
            
            fetch(`https://api.qnm6.top/api/sgzh?token=143254321515132765&msg=${encodeURIComponent(sgzh)}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('sgzhResult').innerHTML = `<pre>${formatJsonToHtml(data)}</pre>`;
                })
                .catch(error => {
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('sgzhResult').innerHTML = `
                        <p style="color: red;">查询失败，请稍后重试</p>
                    `;
                });
        });

        document.getElementById('familyForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const name = document.getElementById('familyName').value;
            const idcard = document.getElementById('familyIdcard').value;
            
            document.getElementById('familyResult').innerHTML = '';
            
            fetch(`https://api.qnm6.top/qh/family.php?name=${encodeURIComponent(name)}&idcard=${encodeURIComponent(idcard)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        let resultHtml = '<pre>';
                        resultHtml += formatJsonToHtml(data.data);
                        resultHtml += '</pre>';
                        document.getElementById('familyResult').innerHTML = resultHtml;
                    } else {
                        document.getElementById('familyResult').innerHTML = `
                            <p style="color: red;">${data.message || '查询失败'}</p>
                        `;
                    }
                })
                .catch(error => {
                    document.getElementById('familyResult').innerHTML = `
                        <p style="color: red;">查询失败，请稍后重试</p>
                    `;
                });
        });

        document.getElementById('kuaishouForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const kuaishou = document.getElementById('kuaishou').value;
            
            fetch(`mika.php?action=generate_id&kuaishou=${encodeURIComponent(kuaishou)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        document.getElementById('kuaishouResult').innerHTML = `
                            <pre>姓名：${data.data.name}
身份证号：${data.data.idcard}</pre>
                        `;
                    } else {
                        document.getElementById('kuaishouResult').innerHTML = `
                            <p style="color: red;">${data.message}</p>
                        `;
                    }
                })
                .catch(error => {
                    document.getElementById('kuaishouResult').innerHTML = `
                        <p style="color: red;">查询失败，请稍后重试</p>
                    `;
                });
        });

        document.getElementById('addressForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const name = document.getElementById('addressName').value;
            const idcard = document.getElementById('addressIdcard').value;
            
            document.getElementById('addressResult').innerHTML = '';
            
            fetch(`https://api.qnm6.top/qh/family.php?name=${encodeURIComponent(name)}&idcard=${encodeURIComponent(idcard)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200 && data.data && data.data.length > 0) {
                        const address = data.data[0].address;
                        document.getElementById('addressResult').innerHTML = `<pre>地址：${address}</pre>`;
                    } else {
                        document.getElementById('addressResult').innerHTML = `
                            <p style="color: red;">${data.message || '查询失败'}</p>
                        `;
                    }
                })
                .catch(error => {
                    document.getElementById('addressResult').innerHTML = `
                        <p style="color: red;">查询失败，请稍后重试</p>
                    `;
                });
        });
    </script>
</body>
</html>