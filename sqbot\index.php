<?php

// 解析 TXT 文件
function parseTxtFile($filePath) {
    // 读取TXT文件内容
    $txtContent = file_get_contents($filePath);
    
    // 用两个换行符分割区块
    $blocks = explode("\n\n", $txtContent);
    
    $rules = [];
    foreach ($blocks as $block) {
        $lines = explode("\n", $block);
        $currentRule = [
            'conditions' => [],
            'responses' => [],
            'variables' => []
        ];
        
        foreach ($lines as $line) {
            // 检查是否定义了变量
            if (preg_match('/^([A-Za-z]+):(.+)$/', $line, $matches)) {
                $currentRule['variables'][$matches[1]] = trim($matches[2]);
            }
            // 检查是否是条件语句
            elseif (strpos($line, '如果:') === 0) {
                $currentRule['conditions'][] = substr($line, 3);
            }
            // 检查是否是普通的响应
            else {
                $currentRule['responses'][] = $line;
            }
        }
        
        $rules[] = $currentRule;
    }
    
    return $rules;
}

// 处理用户输入
function processUserInput($input, $rules) {
    $response = "没有匹配的规则";  // 默认回复

    // 遍历规则，检查条件
    foreach ($rules as $rule) {
        // 遍历所有条件
        foreach ($rule['conditions'] as $condition) {
            // 使用正则判断条件是否匹配
            if (preg_match("/$condition/", $input)) {
                // 如果条件匹配，选择相应的回复
                $response = implode("\n", $rule['responses']);
                break 2;  // 找到匹配规则后直接跳出
            }
        }
    }
    
    return $response;
}

// 调用API并替换变量
function callApi($url, $params) {
    // 使用 file_get_contents 或 cURL 调用 API
    $urlWithParams = $url . '?' . http_build_query($params);
    $response = file_get_contents($urlWithParams);
    
    return $response;
}

// 处理 API 调用并返回结果
function processApiResponse($rule, $variables) {
    if (isset($rule['variables']['A']) && isset($rule['variables']['B'])) {
        $A = $rule['variables']['A'];
        $B = $rule['variables']['B'];
        
        // API 替换 $A 为对应的值
        $apiUrl = str_replace('$A', $A, $B);
        $apiResponse = callApi($apiUrl, ['qq' => $A]);

        // 将返回结果放入回复中
        $rule['responses'][] = "调用结果:\n" . $apiResponse;
    }
    
    return $rule['responses'];
}

// 主逻辑
$rules = parseTxtFile('bot_rules.txt');

// 用户输入
$userInput = "日志";

// 处理用户输入并返回响应
$response = processUserInput($userInput, $rules);

// 如果用户请求日志，则进行 API 调用处理
if ($userInput == "日志") {
    // 假设某个规则匹配了日志请求
    $response = processApiResponse($rules[0], ['A' => 10001]);
}

if (is_array($response)) {
    $response = implode("\n", $response);  // 数组转为字符串
}

echo $response;

?>
