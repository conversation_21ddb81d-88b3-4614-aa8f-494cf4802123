<?php
// 数据库连接参数
$host = 'localhost';
$port = '3306';
$user = 'api269sjk135';
$pwd = 'api269sjk135';
$dbname = 'api269sjk135';
$charset = 'utf8';

// 创建数据库连接
$mysqli = new mysqli($host, $user, $pwd, $dbname, $port);

// 检查连接是否成功
if ($mysqli->connect_error) {
    die("连接失败: " . $mysqli->connect_error);
}

// 设置字符集
$mysqli->set_charset($charset);

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // 获取用户提交的数据
    $shareTG = isset($_POST['shareTG']) ? htmlspecialchars($_POST['shareTG']) : '';
    $fileName = isset($_POST['fileName']) ? htmlspecialchars($_POST['fileName']) : '';
    $fileSize = isset($_POST['fileSize']) ? htmlspecialchars($_POST['fileSize']) : '';
    $fileDesc = isset($_POST['fileDesc']) ? nl2br(htmlspecialchars($_POST['fileDesc'])) : ''; // 转换换行符
    $remarks = isset($_POST['remarks']) ? nl2br(htmlspecialchars($_POST['remarks'])) : ''; // 转换换行符
    $shareType = isset($_POST['shareType']) ? htmlspecialchars($_POST['shareType']) : 'py脚本';
    $downloadUrl = isset($_POST['downloadUrl']) ? htmlspecialchars($_POST['downloadUrl']) : '';

    // 格式化时间
    $shareTime = date('Y-m-d H:i:s');

    // 获取当前最大文件id
    $query = "SELECT MAX(文件id) AS max_id FROM 分享";
    $result = $mysqli->query($query);
    $maxId = 0;
    if ($result && $row = $result->fetch_assoc()) {
        $maxId = $row['max_id'];
    }

    // 生成新的文件id
    $newFileId = $maxId + 1;

// 插入数据
$query = "INSERT INTO 分享 (分享人TG, 分享时间, 分享类型, 文件名称, 文件大小, 文件介绍, 备注, 下载次数, 下载url, 文件id) 
          VALUES (?, ?, ?, ?, ?, ?, ?, 0, ?, ?)";

// 修正类型字符串，确保与参数数量匹配
$stmt = $mysqli->prepare($query);

// 绑定9个参数，类型字符串应为9个 `s`
$stmt->bind_param("sssssssss", $shareTG, $shareTime, $shareType, $fileName, $fileSize, $fileDesc, $remarks, $downloadUrl, $newFileId);

// 执行查询
if ($stmt->execute()) {
    echo "<script>alert('分享记录添加成功！'); window.location.href = 'index.php';</script>";
} else {
    echo "<script>alert('添加失败，请重试！');</script>";
}

$stmt->close();

}

// 关闭数据库连接
$mysqli->close();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加分享记录</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css">
    <style>
        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin-top: 20px;
        }
        .form-container {
            background-color: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        .btn-submit {
            background-color: #007aff;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            width: 100%;
        }
        .btn-submit:hover {
            background-color: #005bb5;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="form-container">
        <h2 class="mb-4 text-center">添加分享记录</h2>
        <form action="../py/sc.php" method="POST">
            <div class="form-group">
                <label for="shareTG">分享人TG</label>
                <input type="text" class="form-control" id="shareTG" name="shareTG" required>
            </div>
            <div class="form-group">
                <label for="fileName">文件名称</label>
                <input type="text" class="form-control" id="fileName" name="fileName" required>
            </div>
            <div class="form-group">
                <label for="fileSize">文件大小</label>
                <input type="text" class="form-control" id="fileSize" name="fileSize" required>
            </div>
            <div class="form-group">
                <label for="shareType">分享类型</label>
                <select class="form-control" id="shareType" name="shareType" required>
                    <option value="py脚本">py脚本</option>
                    <option value="app软件">app软件</option>
                    <option value="api接口">api接口</option>
                    <option value="文本思路">文本思路</option>
                </select>
            </div>
            <div class="form-group">
                <label for="fileDesc">文件介绍</label>
                <textarea class="form-control" id="fileDesc" name="fileDesc" rows="5" required></textarea>
                <small class="form-text text-muted">如果有换行，请直接输入 "Enter"。</small>
            </div>
            <div class="form-group">
                <label for="remarks">备注</label>
                <textarea class="form-control" id="remarks" name="remarks" rows="5" required></textarea>
                <small class="form-text text-muted">如果有换行，请直接输入 "Enter"。</small>
            </div>
            <div class="form-group">
                <label for="downloadUrl">下载链接</label>
                <input type="url" class="form-control" id="downloadUrl" name="downloadUrl" required>
            </div>
            <button type="submit" class="btn-submit mt-3">提交分享记录</button>
        </form>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
