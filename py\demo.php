<?php
// 数据库连接参数
$host = 'localhost';
$port = '3306';
$user = 'api269sjk135';
$pwd = 'api269sjk135';
$dbname = 'api269sjk135';
$charset = 'utf8';

// 创建数据库连接
$mysqli = new mysqli($host, $user, $pwd, $dbname, $port);

// 检查连接是否成功
if ($mysqli->connect_error) {
    die("连接失败: " . $mysqli->connect_error);
}

// 设置字符集
$mysqli->set_charset($charset);

// 获取 id 参数
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($id > 0) {
    // 查询指定 id 的分享数据
    $query = "SELECT * FROM 分享 WHERE 文件id = $id";
    $result = $mysqli->query($query);

    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();

        // 获取数据库中的内容
        $shareTG = htmlspecialchars($row['分享人TG']);  // 避免 XSS
        $shareTime = htmlspecialchars($row['分享时间']);
        $fileType = htmlspecialchars($row['分享类型']);
        $fileName = htmlspecialchars($row['文件名称']);
        $fileSize = htmlspecialchars($row['文件大小']);
        $fileDesc = $row['文件介绍'];  // 已经是 <br /> 标签，不需要 nl2br() 处理
        $remarks = $row['备注'];  // 已经是 <br /> 标签，不需要 nl2br() 处理
        $downloadCount = (int)$row['下载次数'];
        $downloadUrl = htmlspecialchars($row['下载url']);

        // 更新下载次数
        $newDownloadCount = $downloadCount + 1;
        $updateQuery = "UPDATE 分享 SET 下载次数 = $newDownloadCount WHERE 文件id = $id";
        $mysqli->query($updateQuery);
    } else {
        echo "没有找到该分享资源。";
        exit;
    }
} else {
    echo "无效的ID参数。";
    exit;
}

// 关闭数据库连接
$mysqli->close();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分享详情</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css">
    <style>
        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin-top: 20px;
        }
        .share-card {
            background-color: #fff;
            border-radius: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .share-card-header {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .share-card-body {
            margin-bottom: 15px;
            color: #555;
        }
        .share-card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .btn-download {
            background-color: #007aff;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
        }
        .btn-download:hover {
            background-color: #005bb5;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="share-card">
        <div class="share-card-header">
            <?php echo $fileName; ?>
        </div>
        <div class="share-card-body">
            <p><strong>分享人：</strong><?php echo $shareTG; ?></p>
            <p><strong>分享时间：</strong><?php echo $shareTime; ?></p>
            <p><strong>分享类型：</strong><?php echo $fileType; ?></p>
            <p><strong>文件大小：</strong><?php echo $fileSize; ?></p>
            <p><strong>文件介绍：</strong><br /><?php echo $fileDesc; ?></p> <!-- 直接输出 HTML -->
            <p><strong>备注：</strong><br /><?php echo $remarks; ?></p> <!-- 直接输出 HTML -->
            <p><strong>下载次数：</strong><?php echo $newDownloadCount; ?> 次</p>
        </div>
        <div class="share-card-footer">
            <a href="<?php echo $downloadUrl; ?>" class="btn-download" target="_blank">点击下载</a>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
