# 基础信息查询系统

一个基于卡密验证的信息查询系统，采用iOS 18设计风格，支持多端自适应布局。

## 🌟 特性

- **🔑 卡密验证系统** - 安全的访问控制，支持使用次数管理
- **📱 响应式设计** - 完美适配手机、平板、电脑等设备
- **🎨 iOS 18设计风格** - 现代化的用户界面设计
- **🔄 双模式支持** - 演示模式和生产模式可切换
- **🖼️ 图片生成** - 自动生成查询结果图片
- **📊 日志记录** - 完整的操作日志记录

## 📁 文件结构

```
├── index.html              # 主查询页面
├── config.html             # 系统配置页面
├── guide.html              # 使用指南页面
├── admin.html              # 管理界面（可选）
├── api_wrapper.php         # 生产模式API包装器
├── demo_api.php            # 演示模式API
├── generate_demo_image.php # 演示图片生成器
├── generate_api_cards.py   # 卡密生成和管理脚本
├── api_cards.json          # 卡密数据存储
├── api_logs/               # 日志目录
└── README.md               # 说明文档
```

## 🚀 快速开始

### 1. 环境要求

- PHP 7.0+
- Python 3.6+（用于卡密管理）
- Web服务器（Apache/Nginx）

### 2. 安装步骤

1. **下载文件**
   ```bash
   # 将所有文件上传到Web服务器目录
   ```

2. **设置权限**
   ```bash
   chmod 755 *.php
   chmod 666 api_cards.json
   chmod 755 api_logs/
   ```

3. **配置API**
   - 编辑 `api_wrapper.php` 中的上游API配置
   - 设置正确的API地址和Token

4. **生成卡密**
   ```bash
   python generate_api_cards.py
   ```

### 3. 使用方法

1. 访问 `index.html` 开始使用
2. 点击"系统配置"切换演示/生产模式
3. 输入卡密、姓名、身份证号进行查询
4. 查看结果和生成的图片

## 🔧 配置说明

### 演示模式
- 使用 `demo_api.php`
- 返回模拟数据
- 生成演示图片
- 无需配置真实API

### 生产模式
- 使用 `api_wrapper.php`
- 连接真实上游API
- 需要配置API地址和Token

### 卡密管理
使用Python脚本管理卡密：
```bash
python generate_api_cards.py
```

功能包括：
- 生成新卡密
- 查看卡密状态
- 修改使用次数
- 删除卡密
- 导出卡密列表

## 📋 API接口

### 请求格式
```
GET /api_wrapper.php?card_key=卡密&name=姓名&idcard=身份证号&lx=查询类型
```

### 参数说明
| 参数 | 必填 | 说明 |
|------|------|------|
| card_key | 是 | 卡密 |
| name | 是 | 姓名 |
| idcard | 是 | 身份证号（18位） |
| lx | 是 | 查询类型（1=民事诉讼，2=户籍信息） |

### 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "name": "张三",
    "idcard": "110101199001011234",
    "fake_address": "虚假地址",
    "real_address": "真实地址",
    "image_url": "图片URL"
  },
  "remaining_uses": 4
}
```

## 🎨 设计特色

### iOS 18设计元素
- **圆角设计** - 12px主要圆角，8px次要圆角
- **渐变色彩** - 蓝色到紫色的渐变
- **阴影效果** - 轻微的卡片阴影
- **动画交互** - 平滑的过渡动画
- **响应式布局** - 自适应不同屏幕尺寸

### 色彩系统
- 主色：#007AFF（iOS蓝）
- 次色：#5856D6（iOS紫）
- 成功：#34C759（iOS绿）
- 警告：#FF9500（iOS橙）
- 错误：#FF3B30（iOS红）

## 📱 响应式支持

- **桌面端** (>768px) - 完整功能布局
- **平板端** (768px-480px) - 优化的中等屏幕布局
- **手机端** (<480px) - 紧凑的移动端布局

## 🔒 安全特性

- **卡密验证** - 每次查询验证卡密有效性
- **使用次数控制** - 自动扣减和恢复机制
- **输入验证** - 严格的参数验证
- **错误处理** - 完善的错误处理机制
- **日志记录** - 详细的操作日志

## 📊 日志系统

- **位置** - `api_logs/` 目录
- **格式** - JSON格式，按日期分割
- **内容** - 包含时间戳、卡密、请求参数、响应结果

## 🛠️ 故障排除

### 常见问题

1. **卡密不存在**
   - 检查卡密是否正确
   - 使用Python脚本查看卡密状态

2. **使用次数已用完**
   - 使用Python脚本增加使用次数
   - 生成新的卡密

3. **API调用失败**
   - 检查网络连接
   - 验证API配置
   - 查看错误日志

4. **图片无法显示**
   - 检查图片URL
   - 验证服务器配置
   - 检查文件权限

## 📈 扩展功能

可以根据需要添加：
- 用户管理系统
- 更多查询类型
- 数据统计报表
- 微信/支付宝支付
- 短信验证码
- 邮件通知

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📞 支持

如有问题，请查看使用指南或联系技术支持。
