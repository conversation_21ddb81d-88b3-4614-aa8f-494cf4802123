<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密验证</title>
    <style>
        :root {
            --primary-color: #1890ff;
            --hover-color: #40a9ff;
            --gradient-start: #1890ff;
            --gradient-end: #096dd9;
            --border-radius: 8px;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #f0f2f5 0%, #e6f7ff 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .contact-info {
            background: linear-gradient(to right, var(--gradient-start), var(--gradient-end));
            color: white;
            padding: 12px 24px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .price-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            max-width: 1200px;
            width: 100%;
            margin-bottom: 30px;
        }

        .price-card {
            background: white;
            padding: 20px;
            border-radius: var(--border-radius);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .price-card:hover {
            transform: translateY(-5px);
        }

        .price-card h3 {
            color: var(--primary-color);
            margin: 0 0 10px;
            font-size: 1.2rem;
        }

        .price-card .price {
            font-size: 1.8rem;
            font-weight: bold;
            color: #333;
            margin: 10px 0;
        }

        .login-container {
            background-color: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }

        .login-title {
            text-align: center;
            margin-bottom: 2rem;
            color: #333;
            font-size: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #666;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e8e8e8;
            border-radius: var(--border-radius);
            box-sizing: border-box;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            border-color: var(--primary-color);
            outline: none;
        }

        .submit-btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(to right, var(--gradient-start), var(--gradient-end));
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: transform 0.2s ease;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
        }

        .error-message {
            color: #ff4d4f;
            margin-top: 1rem;
            text-align: center;
            display: none;
            padding: 8px;
            background: #fff2f0;
            border-radius: var(--border-radius);
        }
    </style>
</head>
<body>
    <div class="contact-info">
        购买客服QQ：3826726146
    </div>

    <div class="price-container">
        <div class="price-card">
            <h3>一天会员</h3>
            <div class="price">¥13.14</div>
        </div>
        <div class="price-card">
            <h3>一星期会员</h3>
            <div class="price">¥19.99</div>
        </div>
        <div class="price-card">
            <h3>一月会员</h3>
            <div class="price">¥35.99</div>
        </div>
        <div class="price-card">
            <h3>季度会员</h3>
            <div class="price">¥59.99</div>
            <small>两个月</small>
        </div>
        <div class="price-card">
            <h3>一年会员</h3>
            <div class="price">¥99.99</div>
        </div>
        <div class="price-card">
            <h3>永久会员</h3>
            <div class="price">¥199.99</div>
        </div>
    </div>

    <div class="login-container">
        <h2 class="login-title">卡密验证</h2>
        <form id="cardForm" action="verify_card.php" method="post">
            <div class="form-group">
                <label for="cardKey">请输入卡密：</label>
                <input type="text" id="cardKey" name="cardKey" required>
            </div>
            <button type="submit" class="submit-btn">验证</button>
            <div id="errorMessage" class="error-message"></div>
        </form>
    </div>

    <script>
        document.getElementById('cardForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const cardKey = document.getElementById('cardKey').value;
            fetch('verify_card.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'cardKey=' + encodeURIComponent(cardKey)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = 'main.html';
                } else {
                    const errorMessage = document.getElementById('errorMessage');
                    errorMessage.textContent = data.message || '卡密验证失败';
                    errorMessage.style.display = 'block';
                }
            })
            .catch(error => {
                const errorMessage = document.getElementById('errorMessage');
                errorMessage.textContent = '系统错误，请稍后重试';
                errorMessage.style.display = 'block';
            });
        });
    </script>
</body>
</html>