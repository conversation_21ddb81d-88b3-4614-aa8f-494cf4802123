<?php
// 引入数据库配置文件
require '../db.php';

// 获取动态参数 token 和 time
if (isset($_GET['token']) && isset($_GET['time'])) {
    $token = $_GET['token'];
    $time = $_GET['time'];

    // 检查 time 是否是有效的数字
    if (!is_numeric($time) || $time <= 0) {
        echo json_encode(["code" => 400, "message" => "无效的时间参数"], JSON_UNESCAPED_UNICODE);
        exit();
    }

    // 检查 token 指定的用户是否存在
    $stmt = $mysqli->prepare("SELECT token, vipcode, viptime FROM users WHERE token = ?");
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $user_result = $stmt->get_result();
    
    if ($user_result->num_rows == 0) {
        echo json_encode(["code" => 400, "message" => "用户未注册"], JSON_UNESCAPED_UNICODE);
        exit();
    }

    $user = $user_result->fetch_assoc();

    // 计算新的 VIP 到期时间
    if ($user['vipcode'] == '0' || $user['viptime'] == NULL || strtotime($user['viptime']) < time()) {
        // 用户当前不是会员，或会员已过期
        $new_viptime = date('Y-m-d H:i:s', strtotime("+$time days"));
    } else {
        // 用户当前是会员，延长 VIP 到期时间
        $new_viptime = date('Y-m-d H:i:s', strtotime($user['viptime'] . " + $time days"));
    }

    // 更新用户的 VIP 状态和 VIP 到期时间
    $stmt = $mysqli->prepare("UPDATE users SET vipcode = '1', viptime = ? WHERE token = ?");
    $stmt->bind_param("ss", $new_viptime, $token);
    $stmt->execute();

    // 返回成功信息
    echo json_encode([
        "code" => 200,
        "message" => "VIP到期时间更新成功",
        "user" => [
            "token" => $token,
            "viptime" => $new_viptime
        ]
    ], JSON_UNESCAPED_UNICODE);

    $stmt->close();
} else {
    // 参数缺失
    echo json_encode(["code" => 400, "message" => "请输入正确的token和time参数"], JSON_UNESCAPED_UNICODE);
}

$mysqli->close();
?>
