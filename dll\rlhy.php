<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人脸核验 - 无限次调用</title>

    <!-- 引入 Tailwind CSS（通过 CDN） -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">

    <!-- 引入 Ionicons（通过 CDN） -->
    <link href="https://cdn.jsdelivr.net/npm/ionicons@5.5.2/dist/ionicons.css" rel="stylesheet">
</head>
<body class="bg-gray-100 font-sans">

    <!-- 容器：最大宽度 500px，居中显示 -->
    <div class="max-w-md mx-auto mt-20 bg-white p-6 rounded-2xl shadow-lg">
        <h1 class="text-3xl font-semibold text-center text-gray-900 mb-6">人脸核验 - 无限次调用</h1>

        <!-- 输入表单 -->
        <div class="mb-4">
            <label for="token" class="block text-gray-700 text-sm font-medium">Token</label>
            <input type="text" id="token" placeholder="请输入您的Token"
                class="w-full mt-2 p-3 bg-gray-100 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
        
        <div class="mb-4">
            <label for="name" class="block text-gray-700 text-sm font-medium">姓名</label>
            <input type="text" id="name" placeholder="请输入姓名"
                class="w-full mt-2 p-3 bg-gray-100 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <div class="mb-4">
            <label for="idcard" class="block text-gray-700 text-sm font-medium">身份证号码</label>
            <input type="text" id="idcard" placeholder="请输入身份证号码"
                class="w-full mt-2 p-3 bg-gray-100 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <div class="mb-4">
            <label for="imgurl" class="block text-gray-700 text-sm font-medium">图片外链</label>
            <input type="text" id="imgurl" placeholder="请输入图片外链 URL"
                class="w-full mt-2 p-3 bg-gray-100 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <!-- 核验按钮 -->
        <button id="queryBtn" class="w-full bg-blue-600 text-white py-3 rounded-lg text-lg flex items-center justify-center hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300"
            onclick="verifyFaceData()">
            <ion-icon name="search-outline" class="mr-2 text-xl"></ion-icon>
            核验
        </button>

        <!-- 加载动画：显示核验中的效果 -->
        <div id="loading" class="hidden text-center mt-4">
            <ion-icon name="reload-circle" class="animate-spin text-4xl text-blue-600"></ion-icon>
            <p class="mt-2 text-gray-700">核验中...</p>
        </div>

        <!-- 核验结果展示区域 -->
        <div class="mt-6 bg-gray-50 p-4 rounded-lg shadow-md" id="result" style="display: none;">
            <h3 class="font-semibold text-lg text-gray-800">核验结果：</h3>
            <p id="result-message" class="text-sm text-gray-600"></p>
            <p id="result-name" class="text-sm text-gray-600"></p>
            <p id="result-idcard" class="text-sm text-gray-600"></p>
            <p id="result-similarity" class="text-sm text-gray-600"></p>
            <p id="result-status" class="text-sm text-gray-600"></p>
            <p id="execution-time" class="text-sm text-gray-600"></p>
            <div id="result-image" class="mt-4">
                <!-- 人脸图片展示区域 -->
                <img id="face-image" src="" alt="人脸图片" class="w-full rounded-lg shadow-lg">
            </div>
            <p class="text-blue-600 mt-4">官方频道：<a href="https://t.me/kmhsgk" target="_blank">@kmhsgk</a></p>
            <p class="text-blue-600">官网链接：<a href="https://qnm8.top/" target="_blank">https://qnm8.top/</a></p>
        </div>
    </div>

    <script>
        // 获取输入数据并调用 API
        function verifyFaceData() {
            const token = document.getElementById('token').value;
            const name = document.getElementById('name').value;
            const idcard = document.getElementById('idcard').value;
            const imgurl = document.getElementById('imgurl').value;

            // 检查输入字段是否为空
            if (!token || !name || !idcard || !imgurl) {
                alert('请填写所有字段');
                return;
            }

            // 禁用按钮并显示加载动画
            document.getElementById('queryBtn').disabled = true;
            document.getElementById('loading').classList.remove('hidden');
            document.getElementById('result').style.display = 'none';

            const url = `https://qnm8.top/api/rlhy?token=${token}&name=${name}&idcard=${idcard}&imgurl=${imgurl}`;

            // 使用 fetch API 获取数据
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    // 核验完成后，恢复按钮状态并隐藏加载动画
                    document.getElementById('queryBtn').disabled = false;
                    document.getElementById('loading').classList.add('hidden');

                    if (data.code === 200) {
                        // 显示核验结果
                        document.getElementById('result').style.display = 'block';
                        document.getElementById('result-message').textContent = data.message;
                        document.getElementById('result-name').textContent = `姓名: ${data.name}`;
                        document.getElementById('result-idcard').textContent = `身份证: ${data.idcard}`;
                        document.getElementById('result-similarity').textContent = `相似度: ${data.similarity}%`;
                        document.getElementById('result-status').textContent = `核验结果: ${data.result}`;
                        document.getElementById('execution-time').textContent = `执行时间: ${data.execution_time}`;

                        // 显示人脸图片
                        const faceImage = document.getElementById('face-image');
                        faceImage.src = imgurl;  // 使用传入的图片外链
                    } else {
                        alert('核验失败: ' + data.message);
                    }
                })
                .catch(error => {
                    // 核验失败后，恢复按钮状态并隐藏加载动画
                    document.getElementById('queryBtn').disabled = false;
                    document.getElementById('loading').classList.add('hidden');
                    alert('请求失败: ' + error.message);
                });
        }
    </script>

</body>
</html>
