/* 页面基本样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #F4F4F4; /* 淡灰色背景 */
}

/* 顶栏设计 */
.topbar {
    background-color: rgba(255, 255, 255, 0.8); /* 半透明背景 */
    backdrop-filter: blur(10px); /* 毛玻璃效果 */
    color: #000;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 添加阴影 */
}

.topbar h1, .topbar p {
    margin: 0;
}

/* 内容样式 */
.content {
    padding: 20px;
    padding-bottom: 100px; /* 确保底栏不会遮挡列表内容 */
    max-width: 1200px; /* 设置内容区的最大宽度 */
    margin: 0 auto; /* 让内容居中显示 */
}

/* 外部容器，给列表项增加左右小边距 */
.list-container {
    padding: 0 16px; /* 左右边距，保持小边距 */
    box-sizing: border-box; /* 确保内边距包含在宽度内 */
    margin: 0 auto;
}

/* 列表项整体样式 */
.list-item {
    background-color: rgba(255, 255, 255, 0.95); /* 半透明白色 */
    backdrop-filter: blur(15px); /* 毛玻璃效果 */
    padding: 12px 16px; /* 内边距，左右填充，保持小边距 */
    margin-bottom: 12px; /* 底部间距 */
    margin-top: 12px; /* 顶部间距 */
    border-radius: 16px; /* 圆角，适当增加 */
    display: flex;
    align-items: flex-start; /* 上对齐 */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* 轻微的阴影 */
    transition: transform 0.3s, box-shadow 0.3s; /* 过渡效果 */
    max-width: 100%; /* 最大宽度 */
    box-sizing: border-box; /* 包括内边距和边框 */
}

/* 左边图标 */
.list-item-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    margin-right: 12px;
    background-color: rgba(240, 240, 240, 0.6); /* 占位符灰色 */
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1); /* 图标阴影效果 */
}

/* 图标内部图像 */
.list-item-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px; /* 图标圆角 */
}

/* 右边内容布局 */
.list-item-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1;
}

/* 标题 */
.list-item-title {
    font-size: 16px;
    font-weight: bold;
    color: #007AFF; /* iOS 蓝色 */
    margin-bottom: 4px;
}

/* 功能介绍 */
.list-item-description {
    font-size: 14px;
    color: #000000;
    line-height: 1.5;
}

/* 悬停效果 */
.list-item:hover {
    transform: translateY(-4px); /* 悬停时上移 */
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2); /* 悬停时阴影效果 */
}

/* 自适应设计 */
@media (max-width: 768px) {
    .list-item {
        flex-direction: row;
    }

    .list-item-icon {
        margin-right: 12px;
    }

    .list-item-content {
        flex: 1;
    }

    /* 小屏幕下，列表项的左右边距减少 */
    .list-container {
        padding: 0 10px;
    }
}

/* 链接样式 */
.list-item-link {
    text-decoration: none;
    color: inherit;
}

.list-item-link:hover .list-item {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

/* 底部导航栏 */
.bottom-bar {
    position: sticky;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.8); /* 半透明白色背景 */
    backdrop-filter: blur(10px); /* 毛玻璃效果 */
    display: flex;
    justify-content: space-around;
    padding: 10px 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    z-index: 1000; /* 确保底栏在页面的最前面 */
    max-width: 1200px; /* 设置底栏最大宽度 */
    margin: 0 auto; /* 使底栏居中 */
}

.bottom-bar-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.bottom-bar-item img {
    width: 25px;
    height: 25px;
    margin-bottom: 5px;
}

.bottom-bar-item a {
    text-decoration: none;
    color: #fcfcfc; /* iOS 蓝色 */
    font-size: 14px;
}

/* 底部导航栏点击状态的高亮效果 */
.bottom-bar-item a.active {
    color: #FF9500; /* 高亮橙色 */
}

.bottom-bar-item img.active {
    filter: brightness(1.5); /* 高亮图标 */
}

/* 自适应设计，确保在小屏设备上良好显示 */
@media (max-width: 768px) {
    .bottom-bar {
        padding: 15px 0;
    }

    .bottom-bar-item img {
        width: 20px;
        height: 20px;
    }
}
