<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时空间API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        input, button {
            margin: 5px;
            padding: 8px;
        }
        
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>临时空间API测试</h1>
    
    <div class="section">
        <h2>1. 创建临时空间</h2>
        <input type="text" id="createToken" placeholder="输入Token" value="test_token">
        <button onclick="createSpace()">创建空间</button>
        <div id="createResult" class="result"></div>
    </div>
    
    <div class="section">
        <h2>2. 查看图片</h2>
        <input type="text" id="viewToken" placeholder="输入Token" value="test_token">
        <input type="text" id="viewPw" placeholder="输入密码">
        <button onclick="viewImages()">查看图片</button>
        <div id="viewResult" class="result"></div>
    </div>

    <script>
        async function createSpace() {
            const token = document.getElementById('createToken').value;
            const resultDiv = document.getElementById('createResult');
            
            try {
                const response = await fetch(`/extend/api/tmpspace/?token=${encodeURIComponent(token)}&action=create`);
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
                
                // 如果创建成功，自动填充密码到查看区域
                if (data.code === 200) {
                    document.getElementById('viewPw').value = data.pw;
                }
            } catch (error) {
                resultDiv.textContent = '请求失败: ' + error.message;
            }
        }
        
        async function viewImages() {
            const token = document.getElementById('viewToken').value;
            const pw = document.getElementById('viewPw').value;
            const resultDiv = document.getElementById('viewResult');
            
            try {
                const response = await fetch(`/extend/api/tmpspace/?token=${encodeURIComponent(token)}&pw=${encodeURIComponent(pw)}&action=view`);
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.textContent = '请求失败: ' + error.message;
            }
        }
    </script>
</body>
</html>
