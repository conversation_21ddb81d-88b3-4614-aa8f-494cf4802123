<?php 
// Telegram bot token
$token = "**********************************************"; // 您的 Bot token
$apiURL = "https://api.telegram.org/bot$token/"; // Telegram API 的基础 URL

// 获取 webhook 推送的数据
$content = file_get_contents("php://input"); // 获取 Telegram 推送的消息内容
$update = json_decode($content, true); // 解析 JSON 格式的数据
$channel_username = "@kmhsgk";
$group_username = "@kmhsgkq";
// 检查是否有新消息
if (isset($update["message"])) {
    $chat_id = $update["message"]["chat"]["id"]; // 获取聊天 ID
    $message_text = trim($update["message"]["text"]); // 获取消息文本并去除两端空格
    
    // 获取发送消息的用户 ID
    $user_id = $update["message"]["from"]["id"]; // 获取发送该消息的用户 ID
    
    // 机器人用户名
    $bot_name = "@qnmeisgk1_bot"; // 替换成您的机器人实际用户名
    
    // 检查消息中是否包含 @botname
    if (strpos($message_text, $bot_name) !== false) { // 判断是否包含 @botname
        // 提取实际指令（去掉 @botname 部分）
        $message_text = trim(str_replace($bot_name, '', $message_text));

    // 当用户发送 "/start" 时执行的操作
    }if ($message_text == "/start") {
         if (!isUserInChannelAndGroup($user_id)) {
        promptUserToJoinChannelAndGroup($chat_id);
        return;
    }
        handleStartCommand($chat_id);
    }
elseif (preg_match('/^\/lm\s+([\x{4e00}-\x{9fa5}]{2,4})$/u', $message_text, $matches)) {
     if (!isUserInChannelAndGroup($user_id)) {
        promptUserToJoinChannelAndGroup($chat_id);
        return;
    }
    
    $name = trim($matches[1]); // 去除姓名的两端空格
    if (isValidName($name)) {
        handleLmCommand($chat_id, $name);
    } else {
        sendMessage($chat_id, "<b>❌ 无效的姓名。</b>\n\n<b>姓名：</b> 大于一个字且小于或等于四个字的纯中文。
");
    }
} 

    // 处理 "/gh1" 指令
    elseif (preg_match('/^\/gh1\s+(.+?)\s+(\d{17}[\d|Xx])$/', $message_text, $matches)) {
         if (!isUserInChannelAndGroup($user_id)) {
        promptUserToJoinChannelAndGroup($chat_id);
        return;
    }
        $name = trim($matches[1]); // 去除姓名的两端空格
        $id_card = trim($matches[2]); // 去除身份证的两端空格
        if (isValidName($name) && isValidIdCard($id_card)) {
            handleGh1Command1($chat_id, $name, $id_card);
        } else {
            sendMessage($chat_id, "<b>❌ 无效的姓名或身份证号码。</b>\n\n<b>姓名：</b> 大于一个字且小于或等于五个字的纯中文。\n<b>身份证：</b> 18 位纯数字或 17 位带 X。");
        }
    }
    
    // 处理 "/gh2" 指令
    elseif (preg_match('/^\/gh2\s+(.+?)\s+(.+?)\s+(\d{17}[\d|Xx])$/', $message_text, $matches)) {
        if (!isUserInChannelAndGroup($user_id)) {
        promptUserToJoinChannelAndGroup($chat_id);
        return;
    }
        
        
        
        $name = trim($matches[1]); // 去除姓名的两端空格
        $ethnicity = trim($matches[2]); // 去除民族的两端空格
        $id_card = trim($matches[3]); // 去除身份证的两端空格
        if (isValidName($name) && isValidEthnicity($ethnicity) && isValidIdCard($id_card)) {
            handleGh2Command2($chat_id, $name,$id_card , $ethnicity);
        } else {
            sendMessage($chat_id, "<b>❌ 无效的姓名、民族或身份证号码。</b>\n\n<b>姓名：</b> 大于一个字且小于或等于五个字的纯中文。\n<b>民族：</b> 两个字的纯中文。\n<b>身份证：</b> 18 位纯数字或 17 位带 X。
");
        }
    } elseif (preg_match('/^\/kp$/', $message_text)) {
    // 获取用户的 token
     if (!isUserInChannelAndGroup($user_id)) {
        promptUserToJoinChannelAndGroup($chat_id);
        return;
    }
    $user_data = getUserData($chat_id);
    $vipcode = $user_data['user']['vipcode'];

    // 调用接口
    $url = "https://qnmei8.cpolar.top/api/demo.php?token=$chat_id";
    $response = file_get_contents($url);
    $data = json_decode($response, true);

    if ($data['code'] == 200) {
        $user = $data['user'];

        // 如果 vipcode 为 1，发送随机 mp3 语音
        if ($user['vipcode'] == 1) {
            sendMessage($chat_id, "录音文件较大，发送时间可能较长\n\n⏳ 预计发送时长: 30 秒\n\n请耐心等待!");
            $mp3_files = glob('ly/*.mp3'); // 获取 ly 目录下所有 MP3 文件
            $random_mp3 = $mp3_files[array_rand($mp3_files)]; // 随机选择一个 MP3 文件

            // 记录开始时间
            $start_time = microtime(true);

            // 发送语音文件
            sendAudio($chat_id, $random_mp3, "🎉 吹泡泡录音获取成功！");

            // 计算耗时
            $end_time = microtime(true);
            $execution_time = round($end_time - $start_time, 4); // 保留四位小数

            sendMessage($chat_id, "⏱️ 耗时: {$execution_time} 秒");
        } else {
            sendMessage($chat_id, "❌ 您不是会员，无法获取语音。");
        }
    } else {
        sendMessage($chat_id, "❌ 获取信息失败: {$data['message']}");
    }
}
elseif (preg_match('/^\/zh\s+(.+)$/', $message_text, $matches)) {
     if (!isUserInChannelAndGroup($user_id)) {
        promptUserToJoinChannelAndGroup($chat_id);
        return;
    }
    
    
    $query = trim($matches[1]); // 获取用户查询信息
    sendMessage($chat_id, "🔄 处理中，请稍后...");

    // 获取用户的 token
    $user_data = getUserData($chat_id);
    $token = $user_data['user']['token'];
   $start_time = microtime(true);
    // 调用接口进行查询
    $url = "https://qnmei8.cpolar.top/appgn/bl2.php?token=$token&msg=$query";
    $response = file_get_contents($url);
    $data = json_decode($response, true);

    if ($data['code'] == 200) {
        // 如果查询成功，生成临时文件
        $file_name = $query . "-查询结果.txt";
        $shuju = $data['shuju'];

        // 将数据写入临时文件
        $file_path = "/tmp/$file_name";
        file_put_contents($file_path, $shuju);

       // 计算耗时
            $end_time = microtime(true);
            $execution_time = round($end_time - $start_time, 4); // 保留四位小数

        // 发送文件给用户
        sendFile($chat_id, $file_path, "📊 综合信息查询结果：\n$query\n\n📂 结果已写入文件\n\n⏱️ 耗时：\t{$execution_time} 秒");
    } else {
        // 如果查询失败，直接发送错误信息
        sendMessage($chat_id, "❌ 查询失败: {$data['message']}");
    }
}elseif (preg_match('/^\/eys\s+(\p{Han}{2,5})\s+(\d{17}[0-9Xx]|\d{18})$/u', $message_text, $matches)) {
    if (!isUserInChannelAndGroup($user_id)) {
        promptUserToJoinChannelAndGroup($chat_id);
        return;
    }
$user_data = getUserData($chat_id);
    $token = $user_data['user']['token'];

    // 第一步，调用 demo.php 获取用户信息
    $url = "https://qnmei8.cpolar.top/api/demo.php?token=$token";
    $response = file_get_contents($url);
    $data = json_decode($response, true);

    // 检查是否为会员
    if ($data['user']['vipcode'] != 1) {
        // 如果不是会员，直接回复提示信息
        sendMessage($chat_id, "❌ 您不是会员, 无法使用!");
        return;
    }
    $name = trim($matches[1]); // 获取用户输入的姓名
    $id = strtoupper(trim($matches[2])); // 获取用户输入的身份证，并转换为大写

    sendMessage($chat_id, "🔄 处理中，请稍后...");

    $start_time = microtime(true);
    // 调用接口进行二要素验证
    $url = "https://qnmei8.cpolar.top/appgn/eys.php?xm=" . urlencode($name) . "&sfz=" . urlencode($id);
    $response = file_get_contents($url);

    $end_time = microtime(true);
    $execution_time = round($end_time - $start_time, 4); // 保留四位小数

    // 发送结果信息
$result = "🔍 *二要素验证*\n\n"
          . "👤 姓名：\t$name\n"
          . "🆔 身份证：\t$id\n\n"
          . "📋 *验证结果*：\n"
          . "✅ $response\n\n"
          . "⏱️ 耗时：\t{$execution_time} 秒";


    sendMessage($chat_id, $response);
}


} 

elseif (isset($update["callback_query"])) {
    $callback_query_id = $update["callback_query"]["id"];
    $chat_id = $update["callback_query"]["message"]["chat"]["id"];
    $callback_data = $update["callback_query"]["data"];

    // 处理其他回调
    if ($callback_data == "zhcx") {
        sendMessage($chat_id, "<b>🔍 综合信息社工查询</b>\n\n<b>指令如下:</b> ⬇⬇⬇\n\n📝 <code>/zh\t查询内容</code>\n\n查询内容包含: QQ号、手机号、身份证等等.. ");
    }elseif($callback_data == "qggh") {
        sendMessage($chat_id, "<b>🌐 全国个户信息处理</b>\n\n<b>📋 指令如下:</b>\n\n📝 <code>/gh1\t姓名\t身份证</code>\n📝 <code>/gh2\t姓名\t民族\t身份证</code>\n\n我们为全国个户准备了共 2 套模板，您可随意选择使用。
");
    }elseif($callback_data == "xmlm") {
        sendMessage($chat_id, "<b>👤 全国姓名数据猎魔</b>\n\n<b>指令如下:</b> ⬇⬇⬇\n\n📝 <code>/lm\t姓名</code>\n\n您可输入任意姓名，我们会帮您搜索关于 Ta 的信息。
");
    }elseif($callback_data == "kply") {
        sendMessage($chat_id, "<b>🔥 极品少萝御姐卡泡录音</b>\n\n<b>指令如下:</b> ⬇⬇⬇\n\n📝 <code>/kp</code>\n\n此功能不需要递交任何参数，您可以获取到极品卡泡录音，超详细对白对话。\n\n身临其境🔥🔥🔥🔥 
");
    }elseif($callback_data == "eys") {
        sendMessage($chat_id, "<b>🔥 二要素核验</b>\n\n<b>指令如下:</b> ⬇⬇⬇\n\n📝 <code>/eys\t姓名\t身份证</code>\n\n验证该姓名与身份证是否正确符合。\n\n但它是有限制的，会频繁的。如果您希望更快速、更稳定的“批量二要素”，您可以联系机器作者 🤖🚀🔥🔥🔥"

);
    }elseif($callback_data == "joined_channel_and_group") {
      handleStartCommand($chat_id);
    }
}
function sendAudio($chat_id, $audio_path, $caption = '') {
    global $apiURL;

    $post_fields = [
        'chat_id' => $chat_id,
        'caption' => $caption,
        'audio' => new CURLFile($audio_path),
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiURL . "sendAudio");
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_exec($ch);
    curl_close($ch);
}
// 处理 /lm 指令
function handleLmCommand($chat_id, $name) {
    // 记录开始时间
    $start_time = microtime(true);
    sendMessage($chat_id, "🔄 处理中，请稍后...");

    // 获取用户的 token
    global $apiURL;
    $user_data = getUserData($chat_id);
    $token = $user_data['user']['token'];

    // 调用接口
    $url = "https://qnmei8.cpolar.top/appgn/bl2.php?token=$token&msg=$name";
    $response = file_get_contents($url);
    $data = json_decode($response, true);

    if ($data['code'] == 200) {
        // 如果返回成功，创建临时文件并写入数据
        $file_name = $name . "-猎魔结果.txt";
        $shuju = $data['shuju'];
        
        // 将数据写入临时文件
        file_put_contents("/tmp/$file_name", $shuju);
        
        // 计算耗时
        $end_time = microtime(true);
        $execution_time = round($end_time - $start_time, 4); // 计算执行时间，保留4位小数

        // 发送文件给用户
        sendFile($chat_id, "/tmp/$file_name", "$name - 处理结果\n\n⏱️ 耗时：\t{$execution_time} 秒");
    } else {
        // 如果返回失败，直接发送错误信息
        sendMessage($chat_id, "❌ 处理失败: {$data['message']}");
    }
}

// 发送文件给用户
function sendFile($chat_id, $file_path, $caption) {
    global $apiURL;

    $post_fields = [
        'chat_id' => $chat_id,
        'caption' => $caption,
        'document' => new CURLFile($file_path),
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiURL . "sendDocument");
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_exec($ch);
    curl_close($ch);
}


// 校验姓名：大于1个字且小于或等于5个字的纯中文
function isValidName($name) {
    return preg_match('/^[\x{4e00}-\x{9fa5}]{2,5}$/u', $name);
}

// 校验身份证：18位纯数字或17位带X
function isValidIdCard($id_card) {
    return preg_match('/^\d{17}[\d|Xx]$/', $id_card);
}

// 校验民族：两个字的纯中文
function isValidEthnicity($ethnicity) {
    return preg_match('/^[\x{4e00}-\x{9fa5}]{2}$/u', $ethnicity);
}


// 处理 /start 命令
function handleStartCommand($user_id) {
    $user_data = getUserData($user_id);

    if ($user_data['code'] == 200) {
        sendWelcomeMessage($user_id, $user_data);
    } elseif ($user_data['code'] == 404) {
        registerUser($user_id);
        $user_data = getUserData($user_id);

        if ($user_data['code'] == 200) {
            sendWelcomeMessage($user_id, $user_data);
        } else {
            sendMessage($user_id, "❌ 用户注册失败，请稍后重试。".$user_id);
        }
    }
}

// 获取用户数据
function getUserData($user_id) {
    $response = file_get_contents("https://qnmei8.cpolar.top/api/demo.php?token=$user_id");
    return json_decode($response, true);
}

// 注册用户
function registerUser($user_id) {
    $response = file_get_contents("https://qnmei8.cpolar.top/api/zhuce.php?token=$user_id");

    if ($response) {
        // 假设API返回JSON格式数据
        return json_decode($response, true); 
    } else {
        return false;
    }
}


// 发送欢迎消息并添加内联按钮
function sendWelcomeMessage($chat_id, $data) {
    $user = $data['user'];
    $msg = $data['msg'];

    if ($msg['appcode'] != "1") {
        sendMessage($chat_id, "🚧 机器人正在维护，请稍后使用。");
        return;
    }

    $welcome_message = "👋 欢迎您使用 <b>KMH 社工</b>!\n\n🔹 <b>您的 ID</b>: {$user['token']}\n📅 <b>注册时间</b>: {$user['time']}\n💎 <b>会员状态</b>: " . ($user['vipcode'] == "1" ? "是" : "否") . "\n⏰ <b>到期时间</b>: " . ($user['viptime'] ?? "暂无") . "\n🚨 <b>封禁状态</b>: " . ($user['tokencode'] == "200" ? "正常" : "封禁") . "\n";
;

    $inline_keyboard = [
    [
        ["text" => "🔍 综合查询", "callback_data" => "zhcx"],
        ["text" => "🌍 全国个户", "callback_data" => "qggh"],
    ],
    [
        ["text" => "🧑‍🦰 姓名猎魔", "callback_data" => "xmlm"],
        ["text" => "🎤 卡泡录音", "callback_data" => "kply"],
    ],
    [
        ["text" => "🤖二要素核验", "callback_data" => "eys"]
    ],
    [
        ["text" => "✉️ 联系作者", "url" => "https://t.me/yqkh8"]
    ],
    [
        ["text" => "💬 官方群组", "url" => "https://t.me/kmhsgkq"],
        ["text" => "📢 官方频道", "url" => "https://t.me/kmhsgk"]
    ],
    [
        ["text" => "🚀 快捷分享给TG好友", "switch_inline_query" => "全国个户,姓名猎魔,综合查询\n\n最火最牛逼的免费社工库!!\n精准盒打击,点击下方链接免费使用\n\nhttps://t.me/qnmeisgk1_bot"]
    ]
];

    
    sendMessage($chat_id, $welcome_message, "HTML", $inline_keyboard);
}

// 发送消息函数，支持内联按钮
function sendMessage($chat_id, $text, $parse_mode = "HTML", $inline_keyboard = null) {
    global $apiURL;
    
    $post_fields = [
        'chat_id' => $chat_id,
        'text' => $text,
        'parse_mode' => $parse_mode,
    ];

    if ($inline_keyboard) {
        $post_fields['reply_markup'] = json_encode(['inline_keyboard' => $inline_keyboard]);
    }

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiURL . "sendMessage");
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_exec($ch);
    curl_close($ch);
}

function isUserInChannelAndGroup($user_id) {
    global $apiURL, $channel_username, $group_username;

    $channel_member = json_decode(file_get_contents($apiURL . "getChatMember?chat_id=$channel_username&user_id=$user_id"), true);
    $group_member = json_decode(file_get_contents($apiURL . "getChatMember?chat_id=$group_username&user_id=$user_id"), true);

    return ($channel_member['result']['status'] != 'left' && $group_member['result']['status'] != 'left');
}

function promptUserToJoinChannelAndGroup($chat_id) {
    $message = "您需要先加入我们的频道和群组，才能使用机器人";

    $inline_keyboard = [
        [
            ["text" => "📢 加入频道", "url" => "https://t.me/kmhsgk"],
            ["text" => "👥 加入群组", "url" => "https://t.me/kmhsgkq"]
        ],
        [
            ["text" => "我已加入", "callback_data" => "joined_channel_and_group"]
        ]
    ];

    sendMessage($chat_id, $message, "HTML", $inline_keyboard);
}







// 处理 /gh1 指令
function handleGh1Command1($chat_id, $name, $id_card) {
    // 记录开始时间
    $start_time = microtime(true);
    sendMessage($chat_id, "⏳ 开始处理，请稍后! \t");
    // 调用接口
    $url = "https://qnmei8.cpolar.top/plc/bdgh.php?xm=$name&hm=$id_card";
    $response = file_get_contents($url);
    $data = json_decode($response, true);

    if ($data['code'] == "200") {
        $img_url = $data['imgurl'];
        $img_data = file_get_contents($img_url);
        $file_name = basename($img_url);

        // 临时保存图片
        file_put_contents("/tmp/$file_name", $img_data);

        // 计算耗时
        $end_time = microtime(true);
        $execution_time = round($end_time - $start_time, 4); // 计算执行时间，保留4位小数

        // 发送图片
      sendPhoto($chat_id, "/tmp/$file_name", "👤 个户信息：\n$name - $id_card\n🌏 民族: $ethnicity\n✅ Ok! 获取成功\n⏱️ 耗时：{$execution_time} 秒");

    } else {
        sendMessage($chat_id, "❌ 处理失败: {$data['message']}");
    }
}



// 处理 /gh2 指令
function handleGh2Command2($chat_id, $name, $id_card, $ethnicity) {
    // 记录开始时间
    $start_time = microtime(true);
    sendMessage($chat_id, "⏳ 开始处理，请稍后! \t");

    // 调用接口
    $url = "https://qnmei8.cpolar.top/plc/bdgh1.php?xm=$name&mz=$ethnicity&hm=$id_card";
    $response = file_get_contents($url);
    $data = json_decode($response, true);

    if ($data['code'] == "200") {
        $img_url = $data['imgurl'];
        $img_data = file_get_contents($img_url);
        $file_name = basename($img_url);

        // 临时保存图片
        file_put_contents("/tmp/$file_name", $img_data);

        // 计算耗时
        $end_time = microtime(true);
        $execution_time = round($end_time - $start_time, 4); // 计算执行时间，保留4位小数

        // 发送图片
        sendPhoto($chat_id, "/tmp/$file_name", "👤 个户信息：\n$name - $id_card\n🌏 民族: $ethnicity\n✅ 状态：Ok! 获取成功\n\n⏱️ 耗时：\t{$execution_time} 秒");

    } else {
        sendMessage($chat_id, "❌ 处理失败: {$data['message']}");
    }
}
// 发送图片给用户
function sendPhoto($chat_id, $file_path, $caption) {
    global $apiURL;

    $post_fields = [
        'chat_id' => $chat_id,
        'caption' => $caption,
        'photo' => new CURLFile($file_path),
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiURL . "sendPhoto");
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_exec($ch);
    curl_close($ch);
}
?>