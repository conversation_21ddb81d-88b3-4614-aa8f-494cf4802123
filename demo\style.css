/* 通用样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif; /* 英文使用 Roboto */
    background-color: #f5f5f5;
    color: #333;
    display: flex;
    min-height: 100vh;
    flex-direction: column;
    overflow: hidden; /* 禁止页面滚动 */
}

h1, h2, h3, h4, h5, h6, p {
    font-family: 'Noto Sans SC', sans-serif; /* 中文使用 Noto Sans SC */
}

/* 顶栏样式 */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px); /* 高斯模糊效果 */
    border-radius: 10px;
    z-index: 1000;
}

.header .logo {
    font-size: 20px;
    font-weight: bold;
}

.menu-toggle {
    font-size: 30px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

/* 侧滑栏样式 */
.sidebar {
    position: fixed;
    top: 60px; /* 使侧滑栏从顶栏下方开始 */
    left: -250px; /* 初始状态：隐藏 */
    width: 250px;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px); /* 高斯模糊效果 */
    border-radius: 10px 0 0 10px;
    transition: left 0.3s ease;
    padding-top: 20px;
    overflow-y: auto;
    box-shadow: 4px 0 6px rgba(0, 0, 0, 0.1);
    z-index: 1000; /* 保证侧滑栏处于最上层 */
}


.sidebar .sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 2px solid #ccc;
}

.sidebar .sidebar-header .logo {
    font-size: 22px;
    font-weight: bold;
}

.sidebar-links {
    list-style: none;
    padding: 20px;
}

.sidebar-links li {
    margin-bottom: 15px;
}

.sidebar-links a {
    text-decoration: none;
    color: #333;
    font-size: 18px;
    transition: color 0.3s ease;
}

.sidebar-links a:hover {
    color: #0066cc;
}

/* 主内容区域样式 */
.content-frame {
    margin-top: 80px; /* 留出顶部空间 */
    padding: 20px;
    background-color: #ffffff;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    flex-grow: 1;
    transition: margin-left 0.3s ease;
    height: calc(100vh - 80px); /* 限制高度，减去顶部栏高度 */
    overflow-y: auto; /* 允许内容区域滚动 */
}

/* 响应式样式 */
@media (max-width: 768px) {
    .header {
        padding: 0 10px;
    }

    .sidebar {
        width: 200px;
    }

    .sidebar-links a {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .sidebar {
        width: 180px;
    }

    .sidebar-links a {
        font-size: 14px;
    }
}
