<?php

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');
$name = isset($_GET['name']) ? $_GET['name'] : '';
$idcard = isset($_GET['idcard']) ? $_GET['idcard'] : '';
$token = isset($_GET['token']) ? $_GET['token'] : '';

include './verify_vip.php';


// 获取请求中的token
$token = $_GET['token'];  // 或者通过其他方式获取

// 自定义限制（可以自由组合）
$vipTimeLimit = false;  // 是否启用会员时间限制
$vipCodeLimit = true;  // 是否启用会员功能限制

// 定义回调函数
$callback = function($vipcode, $viptime) use ($vipTimeLimit, $vipCodeLimit) {
    if ($vipCodeLimit) {
        // 如果启用会员功能限制，执行相应的检查
        $result = vipCodeLimit($vipcode);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    if ($vipTimeLimit) {
        // 如果启用会员时间限制，执行相应的检查
        $result = vipTimeLimit($viptime);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    return true;  // 如果没有任何限制失败，返回true
};

// 调用验证函数
$verificationResult = verifyVipStatus($token, $callback);

// 如果返回的是错误信息，则输出
if ($verificationResult !== true) {
    echo json_encode($verificationResult, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}




// 验证姓名和身份证格式
function validateName($name) {
    // 姓名必须是2到4个汉字
    return preg_match('/^[\x{4e00}-\x{9fa5}]{2,4}$/u', $name);
}

function validateIdCard($idcard) {
    // 身份证必须是18位纯数字或者17位加上x/X
    return preg_match('/^\d{17}[\dXx]$/', $idcard);
}

// 随机选择的验证函数
function randomValidation($name, $idcard) {
    $functions = ['check_idcard'];
    $selectedFunction = $functions[array_rand($functions)];
    
    // 调用随机选择的函数
    return  $selectedFunction($name, $idcard);
}




// 检查姓名和身份证格式
if (!validateName($name) || !validateIdCard($idcard)) {
    echo json_encode([
        'code' => 401,
        'message' => '请检查姓名或身份证格式',
        '频道' => "@nfgzs",
        '官网' => '【及时更新,更多接口】https://api.qnm6.top/'
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

// 调用 randomValidation 函数并传入动态参数
$result = randomValidation($name, $idcard);


// 根据返回结果输出响应
if ($result === "1") {
    echo json_encode([
        'code' => 200,
        'message' => '二要素核验正确',
        '官方TG频道' => '官方TG频道@idatas8'
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
 
} elseif ($result === "0") {
    echo json_encode([
        'code' => 200,
        'message' => '二要素核验错误',
        '官方TG频道' => '官方TG频道@idatas8'
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
} else {
    echo json_encode([
        'code' => 500,
        'message' => '接口冲突，请重试'
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

}



function check_idcard($name, $idcard) {
    // 构造请求参数并编码
    $params = array(
        'name' => $name,
        'idcard' => $idcard
    );
    $url = 'http://app.nlc.cn/open/online/getSsoIdCard?' . http_build_query($params);
    file_put_contents('123.log', $url, FILE_APPEND);
    // 写入请求数据到日志
    $logData = "请求URL: " . $url . "\n";
    $logData .= "请求参数: " . json_encode($params) . "\n";
    file_put_contents('123.log', $logData, FILE_APPEND);

    // 使用 file_get_contents 发送 GET 请求
    $response = file_get_contents($url);



    // 记录响应数据到日志
    $logData = "响应数据: " . $response . "\n\n";
    file_put_contents('123.log', $logData, FILE_APPEND);

    // 检查响应内容
    return (strpos($response, '成功') !== false) ? "1" : "0";
}



function eys1($name, $idcard) {
    // 设置请求头
    $headers = [
        'User-Agent' => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36',
        'Accept' => 'application/json, text/plain, */*',
        'Accept-Encoding' => 'gzip, deflate',
        'Content-Type' => 'application/x-www-form-urlencoded',
        'Origin' => 'http://ai.exocr.com',
        'Referer' => 'http://ai.exocr.com/ocr/',
        'Accept-Language' => 'zh-CN,zh;q=0.9',
        'Cookie' => 'Hm_lvt_53777574ee88edc8bee6dcd66f2ba5a9=**********; HMACCOUNT=8A5D1B5631421C09; __bid_n=1912c0efc491152f282218; Hm_lpvt_53777574ee88edc8bee6dcd66f2ba5a9=**********; sajssdk_2015_cross_new_user=1; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%221912c0f66d068-009508eb6bf2cbd-17462c6c-280800-1912c0f66d11d8%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E8%87%AA%E7%84%B6%E6%90%9C%E7%B4%A2%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC%22%2C%22%24latest_referrer%22%3A%22https%3A%2F%2Fwww.google.com%2F%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkxMmMwZjY2ZDA2OC0wMDk1MDhlYjZiZjJjYmQtMTc0NjJjNmMtMjgwODAwLTE5MTJjMGY2NmQxMWQ4In0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%221912c0f66d068-009508eb6bf2cbd-17462c6c-280800-1912c0f66d11d8%22%7D'    ];

    // 请求的数据
    $data = [
        'personalName' => $name,
        'identityCardNo' => $idcard
    ];

    // 初始化 cURL
    $ch = curl_init();

    // 设置 cURL 参数
    curl_setopt($ch, CURLOPT_URL, 'http://ai.exocr.com/apqi/api/reco/data/v1/verify_id');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));  // 编码 POST 数据
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    // 执行请求
    $response = curl_exec($ch);

    // 获取 HTTP 响应码
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    // 关闭 cURL 会话
    curl_close($ch);

    // 检查响应状态
    if ($http_code == 200) {
        // 解析 JSON 响应
        $result = json_decode($response, true);
        
        // 判断条件
        if (isset($result['result']['gender']) && $result['result']['gender'] == '0') {
            if (isset($result['result']['result_code']) && $result['result']['result_code'] == 10000) {
                return "1";  // 返回 1，表示认证通过
            } else {
                return "0";  // 返回 0，表示认证未通过
            }
        } else {
            return "0";  // 返回 0，表示认证未通过
        }
    } else {
        // 请求失败，返回错误信息
        return "0";
    }
}


function validateIdCard1($name, $idcard) {
    if (empty($name) || empty($idcard)) {
        return 500; // 参数缺失或无效
    }

    $apiUrl = "https://www.1n.cn/mobile.php/subscriber/isidcard?idcard=" . urlencode($idcard) . "&name=" . urlencode($name);
    $response = @file_get_contents($apiUrl);

    if ($response === false) {
        return "500"; // 请求失败
    }

    $data = json_decode($response, true);

    if (isset($data['status'])) {
        if ($data['status'] == -1) {
            return "0";
        } elseif ($data['status'] == 1) {
            return "1";
        }
    }

    return "500";
}

function validateIdCard2($name, $idcard) {
    if (empty($name) || empty($idcard)) {
        return "500"; // 参数缺失或无效
    }

    $headers = [
        'Host: www.renshenet.org.cn',
        'Accept: application/json, text/plain, */*',
        'Sec-Fetch-Site: same-origin',
        'depCode: 0004',
        'Accept-Language: zh-CN,zh-Hans;q=0.9',
        'Accept-Encoding: gzip, deflate, br',
        'Sec-Fetch-Mode: cors',
        'Content-Type: application/json;charset=UTF-8',
        'Origin: https://www.renshenet.org.cn',
        'User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
        'Referer: https://www.renshenet.org.cn/jxzhrsdist/index.html',
        'Connection: keep-alive',
        'Sec-Fetch-Dest: empty',
    ];

    $data = json_encode([
        "idcard" => $idcard,
        "name" => $name,
    ]);

    $options = [
        'http' => [
            'method' => 'POST',
            'header' => implode("\r\n", $headers),
            'content' => $data,
            'timeout' => 10,
        ],
    ];

    $context = stream_context_create($options);
    $url = "https://www.renshenet.org.cn/mobile/person/register/checkidcard";
    $response = @file_get_contents($url, false, $context);

    if ($response === false) {
        return "500"; // 请求失败
    }

    $data = json_decode($response, true);

    if (isset($data['data']['isSucces'])) {
        return $data['data']['isSucces'] ? "1" : "0";
    }

    return "500";
}

function validateIdCard3($name, $idcard) {
    $baseUrl = "https://web.4399.com/user/?_c=reg&jsoncallback=jQuery172028187463474738483_1714898931471&cid=3000";
    $loginPassword = "0xad39e482653655d7a9ac8a957e067a58";
    $qq = "1367664";
    $encrypt = "1";

    $url = sprintf(
        "%s&login_name=baozinb123&login_password=%s&relogin_pwd=%s&qq=%s&true_name=%s&sfz=%s&encrypt=%s&_=1714898966771",
        $baseUrl,
        urlencode($loginPassword),
        urlencode($loginPassword),
        urlencode($qq),
        urlencode($name),
        urlencode($idcard),
        urlencode($encrypt)
    );

    $response = @file_get_contents($url);

    if ($response === false) {
        return "500"; // 请求失败
    }

    if (strpos($response, "200") !== false) {
        return "1";
    } else {
        return "0";
    }
}



function validateIdCard4($name, $idCard) {
    if (empty($name) || empty($idCard)) {
        return "500"; // 参数缺失或无效
    }

    $url = "http://www.333al.com/?m=ajax&c=update_idcard";
    $data = http_build_query([
        'realname' => $name,
        'id_card' => $idCard
    ]);

    $options = [
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/x-www-form-urlencoded\r\n" .
                        "Content-Length: " . strlen($data) . "\r\n",
            'content' => $data,
            'timeout' => 10,
        ],
    ];

    $context = stream_context_create($options);
    $response = @file_get_contents($url, false, $context);

    if ($response === false) {
        return "500"; // 请求失败
    }

    if (strpos($response, '"code":"1","msg"') !== false) {
        return "1";
    } else {
        return "0";
    }
}

function validateIdCard5($name, $idCard) {
    if (empty($name) || empty($idCard)) {
        return "500"; // 参数缺失或无效
    }

    $url = "http://auth.160sh.com/api//index/?regtype=acc&uname=wkke64646&password=Ww123456489&source=www&type=xy.user.reg&session=0.8183849044527016&time=1714887563" .
           "&realname=" . urlencode($name) . "&idcard=" . urlencode($idCard) .
           "&smccode=&mailcode=&device=1&gid=&channelid=&origin=&ip=***************&xy_channel_type=pt&xy_channelid=www&xy_source_id=www&xy_extra=" .
           "&nickname=&userface=&xy_vip_qq=&xy_vip_weixin=&xy_vip_phone=&xy_package_id=&xy_system=%25E5%25AE%2589%25E5%258D%2593" .
           "&imei=&xy_version=&xy_device_factory=&xy_clipboard=&xy_screen=&xy_language=&xy_uuid=&sign=2bf66e39ed75b1086362afdc9ef49647" .
           "&callback=jQuery341039822046392651567_1714887542174&_=" . time();

    $response = @file_get_contents($url);

    if ($response === false) {
        return "500"; // 请求失败
    }

    if (strpos($response, 'code":20') !== false) {
        return "0";
    } elseif (strpos($response, '15') !== false) {
        return "1";
    } else {
        return "500";
    }
}

function validateIdCard6($name, $idCard) {
    if (empty($name) || empty($idCard)) {
        return "500"; // 参数缺失或无效
    }

    $url = "http://sf3939.bubuwan.com/?m=ajax&c=update_idcard";
    $data = http_build_query([
        'realname' => $name,
        'id_card' => $idCard
    ]);

    $options = [
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/x-www-form-urlencoded\r\n" .
                        "Content-Length: " . strlen($data) . "\r\n",
            'content' => $data,
            'timeout' => 10,
        ],
    ];

    $context = stream_context_create($options);
    $response = @file_get_contents($url, false, $context);

    if ($response === false) {
        return "500";
    }

    if (strpos($response, '-3') !== false) {
        return "0";
    } elseif (strpos($response, '1') !== false) {
        return "1";
    } else {
        return "500";
    }
}






