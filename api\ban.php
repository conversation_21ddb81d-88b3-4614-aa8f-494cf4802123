<?php
// 引入数据库配置文件
require '../db.php';

// 获取动态参数 token 和 code
if (isset($_GET['token']) && isset($_GET['code'])) {
    $token = $_GET['token'];
    $code = $_GET['code'];

    // 检查 token 是否符合条件（仅包含字母和数字）
    if (!preg_match('/^[a-zA-Z0-9]+$/', $token)) {
        echo json_encode(["code" => 400, "message" => "请输入正确的用户名", "user" => null], JSON_UNESCAPED_UNICODE);
        exit();
    }

    // 检查 code 是否为合法值（0 或 1）
    if ($code !== '0' && $code !== '1') {
        echo json_encode(["code" => 400, "message" => "请输入正确的操作码", "user" => null], JSON_UNESCAPED_UNICODE);
        exit();
    }

    // 转换 code 为相应的用户状态
    $tokencode = ($code == '1') ? 500 : 200;

    // 检查数据库中是否存在相同的 token
    $stmt = $mysqli->prepare("SELECT * FROM users WHERE token = ?");
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // 用户存在，更新 tokencode
        $stmt = $mysqli->prepare("UPDATE users SET tokencode = ? WHERE token = ?");
        $stmt->bind_param("is", $tokencode, $token);

        if ($stmt->execute()) {
            // 更新成功，返回信息
            $message = ($code == '1') ? "用户已封禁" : "用户已解封";
            echo json_encode(["code" => 200, "message" => $message, "user" => ["token" => $token, "tokencode" => $tokencode]], JSON_UNESCAPED_UNICODE);
        } else {
            // 更新失败，返回错误信息
            echo json_encode(["code" => 500, "message" => "异常，请稍后重试", "user" => null], JSON_UNESCAPED_UNICODE);
        }
    } else {
        // 用户不存在
        echo json_encode(["code" => 404, "message" => "用户没有注册", "user" => null], JSON_UNESCAPED_UNICODE);
    }

    $stmt->close();
} else {
    // 参数缺失
    echo json_encode(["code" => 400, "message" => "请输入正确的用户名和操作码", "user" => null], JSON_UNESCAPED_UNICODE);
}

$mysqli->close();
?>
