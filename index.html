<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DA查询工具 - 专业信息查询平台</title>
    <style>
        :root {
            --primary-color: #007AFF;
            --secondary-color: #5856D6;
            --success-color: #34C759;
            --warning-color: #FF9500;
            --error-color: #FF3B30;
            --background-color: #F2F2F7;
            --card-background: #FFFFFF;
            --text-primary: #000000;
            --text-secondary: #8E8E93;
            --border-color: #C6C6C8;
            --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.15);
            --border-radius: 12px;
            --border-radius-small: 8px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: var(--background-color);
            color: var(--text-primary);
            line-height: 1.6;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
            font-weight: 400;
        }

        .card {
            background: var(--card-background);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 24px;
            margin-bottom: 24px;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: var(--shadow-hover);
            transform: translateY(-2px);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 16px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-small);
            font-size: 1rem;
            background-color: var(--card-background);
            transition: all 0.3s ease;
            -webkit-appearance: none;
            appearance: none;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 12px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 16px 32px;
            border: none;
            border-radius: var(--border-radius-small);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            min-height: 52px;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            width: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 122, 255, 0.3);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .loading {
            display: none;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .loading.active {
            display: flex;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .result-card {
            display: none;
            background: var(--card-background);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 24px;
            margin-top: 24px;
        }

        .result-card.show {
            display: block;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .result-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
        }

        .status-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            color: white;
        }

        .status-success {
            background-color: var(--success-color);
        }

        .status-error {
            background-color: var(--error-color);
        }

        .result-title {
            font-size: 1.2rem;
            font-weight: 600;
        }

        .result-content {
            display: grid;
            gap: 16px;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #F2F2F7;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-label {
            font-weight: 500;
            color: var(--text-secondary);
        }

        .result-value {
            font-weight: 600;
            text-align: right;
            max-width: 60%;
            word-break: break-all;
        }

        .image-container {
            margin-top: 20px;
            text-align: center;
        }

        .result-image {
            max-width: 100%;
            height: auto;
            border-radius: var(--border-radius-small);
            box-shadow: var(--shadow);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .result-image:hover {
            transform: scale(1.02);
        }

        .alert {
            padding: 16px;
            border-radius: var(--border-radius-small);
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-error {
            background-color: rgba(255, 59, 48, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(255, 59, 48, 0.2);
        }

        .alert-warning {
            background-color: rgba(255, 149, 0, 0.1);
            color: var(--warning-color);
            border: 1px solid rgba(255, 149, 0, 0.2);
        }

        .purchase-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .purchase-modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .purchase-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: var(--border-radius);
            padding: 24px;
            text-align: center;
            position: relative;
            overflow: hidden;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .close-modal {
            position: absolute;
            top: 16px;
            right: 16px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .close-modal:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .purchase-trigger {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            border-radius: 50px;
            padding: 16px 24px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
            transition: all 0.3s ease;
            z-index: 100;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .purchase-trigger:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 122, 255, 0.4);
        }

        .purchase-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            animation: shine 3s infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
            100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        }

        .purchase-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 12px;
            position: relative;
            z-index: 1;
        }

        .purchase-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .price-container {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 20px 0;
            position: relative;
            z-index: 1;
        }

        .price-item {
            background: rgba(255, 255, 255, 0.15);
            border-radius: var(--border-radius-small);
            padding: 16px 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .price-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 4px;
        }

        .price-value {
            font-size: 1.3rem;
            font-weight: 700;
        }

        .limited-offer {
            background: rgba(255, 59, 48, 0.9);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin: 16px 0;
            display: inline-block;
            animation: pulse 2s infinite;
            position: relative;
            z-index: 1;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .btn-purchase {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid white;
            padding: 16px 32px;
            border-radius: var(--border-radius-small);
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
            margin-top: 12px;
        }

        .btn-purchase:hover {
            background: white;
            color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .features-list {
            list-style: none;
            margin: 20px 0;
            position: relative;
            z-index: 1;
        }

        .features-list li {
            margin: 8px 0;
            padding-left: 24px;
            position: relative;
        }

        .features-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #34C759;
            font-weight: bold;
            font-size: 1.2rem;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 16px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .card {
                padding: 20px;
            }

            .result-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }

            .result-value {
                max-width: 100%;
                text-align: left;
            }

            .price-container {
                flex-direction: column;
                gap: 16px;
            }

            .purchase-title {
                font-size: 1.5rem;
            }

            .purchase-trigger {
                bottom: 16px;
                right: 16px;
                padding: 12px 20px;
                font-size: 0.9rem;
            }

            .purchase-card {
                width: 95%;
                padding: 20px;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.8rem;
            }

            .card {
                padding: 16px;
            }

            .form-input, .form-select, .btn {
                padding: 14px;
            }
        }

        /* 暗色模式支持 */
        @media (prefers-color-scheme: dark) {
            :root {
                --background-color: #000000;
                --card-background: #1C1C1E;
                --text-primary: #FFFFFF;
                --text-secondary: #8E8E93;
                --border-color: #38383A;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 DA查询工具</h1>
            <p>专业、快速、准确的信息查询平台</p>
        </div>

        <div class="card">
            <form id="queryForm">
                <div class="form-group">
                    <label class="form-label" for="cardKey">🔑 卡密</label>
                    <input type="text" id="cardKey" name="card_key" class="form-input" placeholder="请输入您的卡密" required>
                </div>

                <div class="form-group">
                    <label class="form-label" for="name">👤 姓名</label>
                    <input type="text" id="name" name="name" class="form-input" placeholder="请输入姓名" required>
                </div>

                <div class="form-group">
                    <label class="form-label" for="idcard">🆔 身份证号</label>
                    <input type="text" id="idcard" name="idcard" class="form-input" placeholder="请输入身份证号" required maxlength="18">
                </div>

                <div class="form-group">
                    <label class="form-label" for="queryType">📋 查询类型</label>
                    <select id="queryType" name="lx" class="form-select" required>
                        <option value="">请选择查询类型</option>
                        <option value="1">民事诉讼信息查询</option>
                        <option value="2">户籍信息查询</option>
                    </select>
                </div>

                <button type="submit" class="btn btn-primary" id="submitBtn">
                    <span class="btn-text">开始查询</span>
                    <div class="loading">
                        <div class="spinner"></div>
                        <span>查询中...</span>
                    </div>
                </button>
            </form>
        </div>

        <div id="resultCard" class="result-card">
            <div class="result-header">
                <div id="statusIcon" class="status-icon"></div>
                <div id="resultTitle" class="result-title"></div>
            </div>
            <div id="resultContent" class="result-content"></div>
        </div>
    </div>

    <!-- 购买卡密弹窗 -->
    <div id="purchaseModal" class="purchase-modal">
        <div class="purchase-card">
            <button class="close-modal" onclick="closePurchaseModal()">×</button>

            <div class="purchase-title">🎯 限时特惠 - 仅剩30份！</div>
            <div class="purchase-subtitle">专业查询工具，助您快速获取准确信息</div>

            <div class="price-container">
                <div class="price-item">
                    <div class="price-label">体验版</div>
                    <div class="price-value">¥13.14</div>
                    <div style="font-size: 0.8rem; margin-top: 4px;">8次查询</div>
                </div>
                <div class="price-item">
                    <div class="price-label">无限版</div>
                    <div class="price-value">¥88</div>
                    <div style="font-size: 0.8rem; margin-top: 4px;">无限查询</div>
                </div>
            </div>

            <ul class="features-list">
                <li>🚀 秒级响应，查询结果实时返回</li>
                <li>📊 多维度数据，信息全面准确</li>
                <li>🔒 数据安全，隐私保护严格</li>
                <li>📱 多端适配，随时随地使用</li>
                <li>🎯 专业团队，技术保障可靠</li>
            </ul>

            <div class="limited-offer">
                ⚡ 限量发售仅30份，售完关闭充值入口！
            </div>

            <!-- <div style="font-size: 0.9rem; opacity: 0.8; margin: 12px 0;">
                ⚠️ 注意：仅关闭充值入口，平台正常运行
            </div> -->

            <a href="https://t.me/MikaJishouBot?start=8034567958" target="_blank" class="btn-purchase">
                🛒 立即购买卡密
            </a>

            <div style="margin-top: 16px; font-size: 0.9rem; opacity: 0.8;">
                💬 点击上方按钮,前往TG机器人购买卡密！
            </div>
        </div>
    </div>

    <!-- 浮动购买按钮 -->
    <button class="purchase-trigger" onclick="openPurchaseModal()">
        💎 购买卡密
    </button>

    <script>
        const form = document.getElementById('queryForm');
        const submitBtn = document.getElementById('submitBtn');
        const btnText = document.querySelector('.btn-text');
        const loading = document.querySelector('.loading');
        const resultCard = document.getElementById('resultCard');
        const statusIcon = document.getElementById('statusIcon');
        const resultTitle = document.getElementById('resultTitle');
        const resultContent = document.getElementById('resultContent');

        // 表单提交处理
        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // 显示加载状态
            setLoadingState(true);
            hideResult();
            
            // 获取表单数据
            const formData = new FormData(form);
            const params = new URLSearchParams(formData);
            
            try {
                // 发送请求
                const response = await fetch(`https://api.qnm6.top/api_wrapper.php?${params}`);
                const result = await response.json();
                
                // 显示结果
                displayResult(result);
                
            } catch (error) {
                // 显示错误
                displayError('网络请求失败，请检查网络连接后重试');
                console.error('Request failed:', error);
            } finally {
                // 隐藏加载状态
                setLoadingState(false);
            }
        });

        // 设置加载状态
        function setLoadingState(isLoading) {
            if (isLoading) {
                submitBtn.disabled = true;
                btnText.style.display = 'none';
                loading.classList.add('active');
            } else {
                submitBtn.disabled = false;
                btnText.style.display = 'block';
                loading.classList.remove('active');
            }
        }

        // 隐藏结果
        function hideResult() {
            resultCard.classList.remove('show');
        }

        // 显示结果
        function displayResult(result) {
            if (result.code === 200) {
                displaySuccess(result);
            } else {
                displayError(result.message || '查询失败');
            }
            
            resultCard.classList.add('show');
            resultCard.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }

        // 显示成功结果
        function displaySuccess(result) {
            statusIcon.className = 'status-icon status-success';
            statusIcon.textContent = '✓';
            resultTitle.textContent = '查询成功';
            
            const data = result.data || {};
            let contentHTML = '';
            
            // 基本信息
            if (data.name) {
                contentHTML += createResultItem('姓名', data.name);
            }
            if (data.idcard) {
                contentHTML += createResultItem('身份证号', data.idcard);
            }
            if (data.fake_address) {
                contentHTML += createResultItem('详细地址', data.fake_address);
            }
            if (data.real_address) {
                contentHTML += createResultItem('三级地址', data.real_address);
            }
            if (result.remaining_uses !== undefined) {
                contentHTML += createResultItem('剩余查询次数', result.remaining_uses + ' 次');
            }
            
            resultContent.innerHTML = contentHTML;
            
            // 显示图片
            if (data.image_url) {
                const imageContainer = document.createElement('div');
                imageContainer.className = 'image-container';
                imageContainer.innerHTML = `
                    <img src="${data.image_url}" alt="查询结果图片" class="result-image" onclick="openImageModal('${data.image_url}')">
                `;
                resultContent.appendChild(imageContainer);
            }
        }

        // 显示错误结果
        function displayError(message) {
            statusIcon.className = 'status-icon status-error';
            statusIcon.textContent = '✗';
            resultTitle.textContent = '查询失败';
            
            resultContent.innerHTML = `
                <div class="alert alert-error">
                    ${message}
                </div>
            `;
        }

        // 创建结果项
        function createResultItem(label, value) {
            return `
                <div class="result-item">
                    <span class="result-label">${label}</span>
                    <span class="result-value">${value}</span>
                </div>
            `;
        }

        // 打开图片模态框
        function openImageModal(imageUrl) {
            window.open(imageUrl, '_blank');
        }

        // 身份证号输入验证
        document.getElementById('idcard').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^\dXx]/g, '');
            if (value.length > 18) {
                value = value.slice(0, 18);
            }
            e.target.value = value;
        });

        // 卡密输入处理
        document.getElementById('cardKey').addEventListener('input', function(e) {
            e.target.value = e.target.value.trim();
        });

        // 弹窗控制
        function openPurchaseModal() {
            document.getElementById('purchaseModal').classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closePurchaseModal() {
            document.getElementById('purchaseModal').classList.remove('show');
            document.body.style.overflow = 'auto';
        }

        // 点击弹窗背景关闭
        document.getElementById('purchaseModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePurchaseModal();
            }
        });

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closePurchaseModal();
            }
        });
    </script>
</body>
</html>
