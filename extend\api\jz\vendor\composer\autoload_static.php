<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInita03b4d3fc1b849a9b6a9a2a4ecc9445d
{
    public static $prefixLengthsPsr4 = array (
        'X' => 
        array (
            '<PERSON><PERSON><PERSON>\\' => 9,
        ),
        'S' => 
        array (
            '<PERSON><PERSON><PERSON>\\Phone\\' => 16,
        ),
        'J' => 
        array (
            'Jxlwqq\\IdValidator\\' => 19,
        ),
    );

    public static $prefixDirsPsr4 = array (
        '<PERSON><PERSON><PERSON>\\' => 
        array (
            0 => __DIR__ . '/../..' . '/',
        ),
        '<PERSON><PERSON><PERSON>\\Phone\\' => 
        array (
            0 => __DIR__ . '/..' . '/shitoudev/phone-location/src',
        ),
        'Jxlwqq\\IdValidator\\' => 
        array (
            0 => __DIR__ . '/..' . '/jxlwqq/id-validator/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInita03b4d3fc1b849a9b6a9a2a4ecc9445d::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInita03b4d3fc1b849a9b6a9a2a4ecc9445d::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInita03b4d3fc1b849a9b6a9a2a4ecc9445d::$classMap;

        }, null, ClassLoader::class);
    }
}
