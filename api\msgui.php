<?php
// 引入数据库连接配置
require_once '../db.php';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $appvid = isset($_POST['appvid']) ? $mysqli->real_escape_string($_POST['appvid']) : '';
    $appcode = isset($_POST['appcode']) ? $mysqli->real_escape_string($_POST['appcode']) : '';
    $appgg = isset($_POST['appgg']) ? $mysqli->real_escape_string($_POST['appgg']) : '';
    $appurl = isset($_POST['appurl']) ? $mysqli->real_escape_string($_POST['appurl']) : '';
    $appimg = isset($_POST['appimg']) ? $mysqli->real_escape_string($_POST['appimg']) : '';
    $dailiurl = isset($_POST['dailiurl']) ? $mysqli->real_escape_string($_POST['dailiurl']) : '';
    $qqqid = isset($_POST['qqqid']) ? $mysqli->real_escape_string($_POST['qqqid']) : '';
    $guanggaourl = isset($_POST['guanggaourl']) ? $mysqli->real_escape_string($_POST['guanggaourl']) : '';
    $gwurl = isset($_POST['gwurl']) ? $mysqli->real_escape_string($_POST['gwurl']) : '';

    // 更新数据库
    $update_query = "UPDATE msg SET appvid='$appvid', appcode='$appcode', appgg='$appgg', appurl='$appurl', appimg='$appimg', dailiurl='$dailiurl', qqqid='$qqqid', guanggaourl='$guanggaourl',gwurl='$gwurl' WHERE 1";

    if ($mysqli->query($update_query)) {
        $message = "设置成功";
    } else {
        $message = "设置失败: " . $mysqli->error;
    }
}

// 获取当前信息
$result = $mysqli->query("SELECT * FROM msg WHERE 1");
$row = $result->fetch_assoc();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置软件信息</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            padding: 20px;
            margin: 0;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: auto;
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 8px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        .form-group button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
        .form-group button:hover {
            background-color: #45a049;
        }
        .message {
            margin-bottom: 20px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>设置软件信息</h1>
        <?php if (isset($message)): ?>
            <div class="message"><?php echo htmlspecialchars($message, ENT_QUOTES, 'UTF-8'); ?></div>
        <?php endif; ?>
        <form method="post">
            <div class="form-group">
                <label for="appvid">软件版本号</label>
                <input type="text" id="appvid" name="appvid" value="<?php echo htmlspecialchars($row['appvid'], ENT_QUOTES, 'UTF-8'); ?>">
            </div>
            <div class="form-group">
                <label for="appcode">软件状态</label>
                <input type="text" id="appcode" name="appcode" value="<?php echo htmlspecialchars($row['appcode'], ENT_QUOTES, 'UTF-8'); ?>">
            </div>
            <div class="form-group">
                <label for="appgg">软件公告</label>
                <textarea id="appgg" name="appgg"><?php echo htmlspecialchars($row['appgg'], ENT_QUOTES, 'UTF-8'); ?></textarea>
            </div>
            <div class="form-group">
                <label for="appurl">软件下载链接</label>
                <input type="text" id="appurl" name="appurl" value="<?php echo htmlspecialchars($row['appurl'], ENT_QUOTES, 'UTF-8'); ?>">
            </div>
            <div class="form-group">
                <label for="appimg">软件背景图链接</label>
                <input type="text" id="appimg" name="appimg" value="<?php echo htmlspecialchars($row['appimg'], ENT_QUOTES, 'UTF-8'); ?>">
            </div>
            <div class="form-group">
                <label for="dailiurl">代理申请网站链接</label>
                <input type="text" id="dailiurl" name="dailiurl" value="<?php echo htmlspecialchars($row['dailiurl'], ENT_QUOTES, 'UTF-8'); ?>">
            </div>
            <div class="form-group">
                <label for="qqqid">官方QQ群号码</label>
                <input type="text" id="qqqid" name="qqqid" value="<?php echo htmlspecialchars($row['qqqid'], ENT_QUOTES, 'UTF-8'); ?>">
            </div>
            <div class="form-group">
                <label for="guanggaourl">广告跳转链接</label>
                <input type="text" id="guanggaourl" name="guanggaourl" value="<?php echo htmlspecialchars($row['guanggaourl'], ENT_QUOTES, 'UTF-8'); ?>">
            </div>
            <div class="form-group">
                <label for="gwurl">官网地址</label>
                <input type="text" id="gwurl" name="gwurl" value="<?php echo htmlspecialchars($row['gwurl'], ENT_QUOTES, 'UTF-8'); ?>">
            </div>
            <div class="form-group">
                <button type="submit">保存</button>
            </div>
        </form>
    </div>
</body>
</html>
