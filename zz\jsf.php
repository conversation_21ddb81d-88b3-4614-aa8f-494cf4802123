<?php
$apiUrl = 'https://smm-center.com/api/v2';  // API URL
$apiKey = '4e65f1b869cc0401f8df745dae947c88';          // API Key

$orderData = [
    'key' => $apiKey,
    'action' => 'add',
    'service' => 22871,
    'link' => 'https://t.me/nfgzs/56',
    'quantity' => 100
];

// 初始化 cURL 请求
$ch = curl_init();

// 设置 cURL 选项
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($orderData));

// 禁用 SSL 证书验证（仅用于测试）
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

// 执行 cURL 请求并获取响应
$response = curl_exec($ch);

// 检查是否发生错误
if (curl_errno($ch)) {
    echo 'cURL 错误: ' . curl_error($ch);
} else {
    // 将响应结果转换为 PHP 数组
    $responseData = json_decode($response, true);
    echo '<pre>';
    print_r($responseData);
    echo '</pre>';
}

// 关闭 cURL 连接
curl_close($ch);
?>
