<?php
// 测试增强版API的功能
header('Content-Type: text/html; charset=utf-8');

echo "<h1>🚀 增强版社工查询API测试（使用专业数据包）</h1>";

// 引入专业包
require_once '../vendor/autoload.php';
use Jxlwqq\IdValidator\IdValidator;
use Shitoudev\Phone\PhoneLocation;

// 测试身份证信息提取
echo "<h2>1. 身份证信息提取测试（使用 jxlwqq/id-validator）</h2>";

// 测试多个身份证号
$testIdCards = [
    "******************", // 广东深圳
    "110101199001011234", // 北京东城
    "310101198001011234", // 上海黄浦
    "610104620927690"     // 15位身份证
];

foreach ($testIdCards as $testIdCard) {
    echo "<h3>🆔 测试身份证: $testIdCard</h3>";

    try {
        $idValidator = new IdValidator();

        // 验证有效性
        $isValid = $idValidator->isValid($testIdCard);
        echo "<p><strong>有效性:</strong> " . ($isValid ? "✅ 有效" : "❌ 无效") . "</p>";

        if ($isValid) {
            // 获取详细信息
            $idInfo = $idValidator->getInfo($testIdCard);

            echo "<div style='background:#f0f8ff; padding:15px; margin:10px 0; border-radius:8px;'>";
            echo "<h4>📋 详细信息:</h4>";
            echo "<ul>";
            echo "<li><strong>地址:</strong> {$idInfo['address']}</li>";
            echo "<li><strong>省市区:</strong> " . implode(' > ', $idInfo['addressTree']) . "</li>";
            echo "<li><strong>出生日期:</strong> {$idInfo['birthdayCode']}</li>";
            echo "<li><strong>星座:</strong> {$idInfo['constellation']}</li>";
            echo "<li><strong>生肖:</strong> {$idInfo['chineseZodiac']}</li>";
            echo "<li><strong>性别:</strong> " . ($idInfo['sex'] == 1 ? '男' : '女') . "</li>";
            echo "<li><strong>证件长度:</strong> {$idInfo['length']}位</li>";
            if ($idInfo['length'] == 18) {
                echo "<li><strong>校验码:</strong> {$idInfo['checkBit']}</li>";
            }
            echo "<li><strong>地址码状态:</strong> " . ($idInfo['abandoned'] ? '已废弃' : '正在使用') . "</li>";
            echo "</ul>";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<p style='color:red;'>❌ 处理出错: " . $e->getMessage() . "</p>";
    }

    echo "<hr>";
}

// 测试手机号信息提取
echo "<h2>2. 手机号信息提取测试（使用 shitoudev/phone-location）</h2>";

$testPhones = [
    "13812345678", // 上海移动
    "18621281566", // 上海联通
    "15912345678", // 广东移动
    "17712345678"  // 虚拟运营商
];

foreach ($testPhones as $testPhone) {
    echo "<h3>📱 测试手机号: $testPhone</h3>";

    try {
        $phoneLocation = new PhoneLocation();
        $locationInfo = $phoneLocation->find($testPhone);

        if (!empty($locationInfo)) {
            echo "<div style='background:#f0fff0; padding:15px; margin:10px 0; border-radius:8px;'>";
            echo "<h4>📍 归属地信息:</h4>";
            echo "<ul>";
            echo "<li><strong>省份:</strong> {$locationInfo['province']}</li>";
            echo "<li><strong>城市:</strong> {$locationInfo['city']}</li>";
            echo "<li><strong>运营商:</strong> {$locationInfo['sp']}</li>";
            echo "<li><strong>邮编:</strong> {$locationInfo['postcode']}</li>";
            echo "<li><strong>区号:</strong> {$locationInfo['tel_prefix']}</li>";
            echo "<li><strong>号段:</strong> " . substr($testPhone, 0, 3) . "</li>";
            echo "<li><strong>完整归属地:</strong> {$locationInfo['province']} {$locationInfo['city']}</li>";
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<p style='color:orange;'>⚠️ 未找到归属地信息</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color:red;'>❌ 处理出错: " . $e->getMessage() . "</p>";
    }

    echo "<hr>";
}

// API调用示例
echo "<h2>3. API调用示例</h2>";
echo "<p>增强版API现在支持以下功能：</p>";
echo "<ul>";
echo "<li><strong>身份证智能扩展</strong>：自动提取出生日期、年龄、性别、星座、归属地区</li>";
echo "<li><strong>手机号智能扩展</strong>：自动识别运营商、号段信息</li>";
echo "<li><strong>关联查询扩展</strong>：通过身份证查找关联手机号，通过手机号查找关联身份证</li>";
echo "<li><strong>数据统计</strong>：提供详细的查询统计信息</li>";
echo "</ul>";

echo "<h3>API调用方式：</h3>";
echo "<code>GET /extend/api/sgzh/index.php?msg=[查询内容]&token=[用户token]</code>";

echo "<h3>返回数据格式：</h3>";
echo "<pre>";
echo json_encode([
    'code' => 200,
    'message' => '查询成功（已智能扩展）',
    'shuju' => '原始数据 + 智能扩展信息 + 关联查询结果',
    'execution_time' => '0.1234 秒',
    'enhanced' => true,
    'stats' => [
        'original_records' => 2,
        'enhanced_info' => 3,
        'cross_reference' => 1,
        'id_cards_found' => 2,
        'phones_found' => 3
    ]
], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
echo "</pre>";

echo "<h2>4. 功能特点</h2>";
echo "<ul>";
echo "<li>🔍 <strong>智能扩展</strong>：基于身份证和手机号自动扩展相关信息</li>";
echo "<li>🔗 <strong>关联查询</strong>：跨表关联查询，发现更多相关数据</li>";
echo "<li>📊 <strong>统计分析</strong>：提供详细的查询统计和数据分析</li>";
echo "<li>🏷 <strong>数据标签</strong>：清晰标识数据来源和类型</li>";
echo "<li>⚡ <strong>性能优化</strong>：保持原有查询性能的同时增加扩展功能</li>";
echo "</ul>";

echo "<h2>5. 支持的查询类型</h2>";
echo "<ul>";
echo "<li><strong>身份证查询</strong>：18位身份证号码</li>";
echo "<li><strong>手机号查询</strong>：11位手机号码</li>";
echo "<li><strong>姓名查询</strong>：中文姓名</li>";
echo "<li><strong>QQ号查询</strong>：6-12位数字</li>";
echo "</ul>";

?>
