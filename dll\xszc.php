<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>刑事侦查 - 高仿真</title>

    <!-- 引入 Tailwind CSS（通过 CDN） -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">

    <!-- 引入 Ionicons（通过 CDN） -->
    <link href="https://cdn.jsdelivr.net/npm/ionicons@5.5.2/dist/ionicons.css" rel="stylesheet">
</head>
<body class="bg-gray-100 font-sans">

    <!-- 容器：最大宽度 500px，居中显示 -->
    <div class="max-w-md mx-auto mt-20 bg-white p-6 rounded-2xl shadow-lg">
        <h1 class="text-3xl font-semibold text-center text-gray-900 mb-6">刑事侦查 - 高仿真</h1>

        <!-- 输入表单 -->
        <div class="mb-4">
            <label for="token" class="block text-gray-700 text-sm font-medium">Token</label>
            <input type="text" id="token" placeholder="请输入您的Token"
                class="w-full mt-2 p-3 bg-gray-100 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <div class="mb-4">
            <label for="xm" class="block text-gray-700 text-sm font-medium">姓名</label>
            <input type="text" id="xm" placeholder="请输入姓名"
                class="w-full mt-2 p-3 bg-gray-100 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <div class="mb-4">
            <label for="hm" class="block text-gray-700 text-sm font-medium">身份证号码</label>
            <input type="text" id="hm" placeholder="请输入身份证号码"
                class="w-full mt-2 p-3 bg-gray-100 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <div class="mb-4">
            <label for="ajxz" class="block text-gray-700 text-sm font-medium">案件性质</label>
            <select id="ajxz" class="w-full mt-2 p-3 bg-gray-100 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="1">轻微犯罪</option>
                <option value="2">普通犯罪</option>
                <option value="3">重大犯罪</option>
                <option value="4">特别重大犯罪</option>
            </select>
        </div>

        <div class="mb-4">
            <label for="ajlb" class="block text-gray-700 text-sm font-medium">案件类别</label>
            <select id="ajlb" class="w-full mt-2 p-3 bg-gray-100 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="1">盗窃案</option>
                <option value="2">暴力案</option>
            </select>
        </div>

        <div class="mb-4">
            <label for="danwei" class="block text-gray-700 text-sm font-medium">工作单位</label>
            <input type="text" id="danwei" placeholder="请输入工作单位"
                class="w-full mt-2 p-3 bg-gray-100 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <div class="mb-6">
            <label for="whcd" class="block text-gray-700 text-sm font-medium">文化程度</label>
            <input type="text" id="whcd" placeholder="请输入文化程度"
                class="w-full mt-2 p-3 bg-gray-100 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <!-- 处理按钮 -->
        <button id="queryBtn" class="w-full bg-blue-600 text-white py-3 rounded-lg text-lg flex items-center justify-center hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300"
            onclick="getXszcData()">
            <ion-icon name="search-outline" class="mr-2 text-xl"></ion-icon>
            处理
        </button>

        <!-- 加载动画：显示处理中的效果 -->
        <div id="loading" class="hidden text-center mt-4">
            <ion-icon name="reload-circle" class="animate-spin text-4xl text-blue-600"></ion-icon>
            <p class="mt-2 text-gray-700">处理中...</p>
        </div>

        <!-- 处理结果展示区域 -->
        <div class="mt-6 bg-gray-50 p-4 rounded-lg shadow-md" id="result" style="display: none;">
            <h3 class="font-semibold text-lg text-gray-800">处理结果：</h3>
            <p id="result-message" class="text-sm text-gray-600"></p>
            <div id="result-img" class="mt-4">
                <!-- 图片展示区域 -->
                <img id="imgurl" class="w-full h-auto rounded-lg shadow-lg" alt="刑事侦查结果图像">
            </div>
            <p class="text-blue-600 mt-4">官方频道：<a href="https://t.me/kmhsgk" target="_blank">@kmhsgk</a></p>
            <p class="text-blue-600">官网链接：<a href="https://qnm8.top/" target="_blank">https://qnm8.top/</a></p>
        </div>
    </div>

    <script>
        // 获取输入数据并调用 API
        function getXszcData() {
            const token = document.getElementById('token').value;
            const xm = document.getElementById('xm').value;
            const hm = document.getElementById('hm').value;
            const ajxz = document.getElementById('ajxz').value;
            const ajlb = document.getElementById('ajlb').value;
            const danwei = document.getElementById('danwei').value;
            const whcd = document.getElementById('whcd').value;

            // 检查输入字段是否为空
            if (!token || !xm || !hm || !ajxz || !ajlb || !danwei || !whcd) {
                alert('请填写所有字段');
                return;
            }

            // 禁用按钮并显示加载动画
            document.getElementById('queryBtn').disabled = true;
            document.getElementById('loading').classList.remove('hidden');
            document.getElementById('result').style.display = 'none';

            const url = `https://qnm8.top/api/xszc?token=${token}&xm=${xm}&hm=${hm}&ajxz=${ajxz}&ajlb=${ajlb}&danwei=${danwei}&whcd=${whcd}`;

            // 使用 fetch API 获取数据
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    // 处理完成后，恢复按钮状态并隐藏加载动画
                    document.getElementById('queryBtn').disabled = false;
                    document.getElementById('loading').classList.add('hidden');

                    if (data.code === "200") {
                        // 显示处理结果
                        document.getElementById('result').style.display = 'block';
                        document.getElementById('result-message').textContent = data.message;
                        
                        // 显示生成的图像
                        const imgElement = document.getElementById('imgurl');
                        imgElement.src = data.imgurl;
                    } else {
                        alert('处理失败: ' + data.message);
                    }
                })
                .catch(error => {
                    // 处理失败后，恢复按钮状态并隐藏加载动画
                    document.getElementById('queryBtn').disabled = false;
                    document.getElementById('loading').classList.add('hidden');
                    alert('请求失败: ' + error.message);
                });
        }
    </script>

</body>
</html>
