<?php
header('Content-Type: application/json');

// 引入必要的库
require_once 'names.php';
require_once 'address.php';

// 处理微信号查询
if (isset($_GET['wechat'])) {
    $wechat = $_GET['wechat'];
    
    // 从names.php中随机获取姓名
    $name = $names[array_rand($names)];
    
    // 生成随机身份证号
    $idcard = generateIdCard();
    
    // 根据身份证号判断性别
    $gender = (substr($idcard, -2, 1) % 2 == 0) ? '女' : '男';
    
    // 计算年龄
    $birthYear = substr($idcard, 6, 4);
    $age = date('Y') - $birthYear;
    
    // 从address.php中随机获取籍贯
    $hometown = $addresses[array_rand($addresses)];
    
    echo json_encode([
        'code' => 200,
        'message' => '查询成功',
        'data' => [
            'name' => $name,
            'idcard' => $idcard,
            'gender' => $gender,
            'age' => $age,
            'hometown' => $hometown
        ]
    ]);
    exit;
}

// 处理婚姻史生成
if (isset($_GET['type']) && $_GET['type'] === 'marriage' && isset($_GET['name']) && isset($_GET['idcard'])) {
    $name = $_GET['name'];
    $idcard = $_GET['idcard'];
    
    // 生成婚姻史
    $history = generateMarriageHistory($name, $idcard);
    
    echo json_encode([
        'code' => 200,
        'message' => '生成成功',
        'data' => [
            'history' => $history
        ]
    ]);
    exit;
}

// 生成身份证号
function generateIdCard() {
    // 随机生成地区码（前6位）
    $areaCode = '43' . str_pad(rand(1, 99), 2, '0', STR_PAD_LEFT) . str_pad(rand(1, 99), 2, '0', STR_PAD_LEFT);
    
    // 随机生成出生日期（中间8位）
    $year = rand(1950, 2000);
    $month = str_pad(rand(1, 12), 2, '0', STR_PAD_LEFT);
    $day = str_pad(rand(1, 28), 2, '0', STR_PAD_LEFT);
    $birthDate = $year . $month . $day;
    
    // 随机生成顺序码（后3位）
    $sequence = str_pad(rand(0, 999), 3, '0', STR_PAD_LEFT);
    
    // 计算校验码
    $base = $areaCode . $birthDate . $sequence;
    $weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    $sum = 0;
    for ($i = 0; $i < 17; $i++) {
        $sum += $base[$i] * $weights[$i];
    }
    $checkCode = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'][$sum % 11];
    
    return $base . $checkCode;
}

// 生成婚姻史
function generateMarriageHistory($name, $idcard) {
    $gender = (substr($idcard, -2, 1) % 2 == 0) ? '女' : '男';
    $birthYear = substr($idcard, 6, 4);
    $age = date('Y') - $birthYear;
    
    // 根据年龄生成合适的婚姻史
    $history = "婚姻登记记录查询结果\n\n";
    $history .= "姓名：{$name}\n";
    $history .= "身份证号：{$idcard}\n";
    $history .= "性别：{$gender}\n";
    $history .= "年龄：{$age}岁\n\n";
    
    if ($age < 22) {
        $history .= "婚姻状况：未婚\n";
        $history .= "说明：根据《中华人民共和国民法典》规定，男性结婚年龄不得早于22周岁，女性不得早于20周岁。\n";
    } else {
        // 随机生成婚姻次数（1-3次）
        $marriageCount = rand(1, 3);
        $history .= "婚姻状况：已婚\n";
        $history .= "婚姻次数：{$marriageCount}次\n\n";
        
        $firstMarriageAge = rand(22, 28);
        $currentYear = date('Y');
        
        for ($i = 1; $i <= $marriageCount; $i++) {
            $marriageYear = $birthYear + $firstMarriageAge + ($i - 1) * rand(2, 5);
            if ($marriageYear >= $currentYear) break;
            
            $history .= "第{$i}次婚姻：\n";
            $history .= "登记时间：{$marriageYear}年" . rand(1, 12) . "月\n";
            $history .= "登记地点：某某市民政局\n";
            
            if ($i < $marriageCount) {
                $divorceYear = $marriageYear + rand(1, 5);
                if ($divorceYear < $currentYear) {
                    $history .= "离婚时间：{$divorceYear}年" . rand(1, 12) . "月\n";
                    $history .= "离婚原因：感情不和\n";
                }
            }
            $history .= "\n";
        }
    }
    
    $history .= "\n查询时间：" . date('Y年m月d日 H:i:s') . "\n";
    $history .= "查询单位：某某市民政局\n";
    $history .= "查询编号：" . date('YmdHis') . rand(1000, 9999) . "\n";
    
    return $history;
}

// 返回错误信息
echo json_encode([
    'code' => 400,
    'message' => '参数错误'
]);
?> 